<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #2c3e50; line-height: 1.6; }
      .highlight-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #e74c3c; font-weight: bold; }
      .app-bg { fill: #f8f9fa; stroke: #e9ecef; stroke-width: 3; rx: 20; }
      .logo-bg { fill: #c62828; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">案例深研：向最优秀的对手学习</text>
  
  <!-- 招商银行Logo区域 -->
  <rect x="150" y="150" width="300" height="200" class="logo-bg" rx="15"/>
  <text x="300" y="230" text-anchor="middle" class="content-text" fill="white">招商银行</text>
  <text x="300" y="270" text-anchor="middle" class="content-text" fill="white">LOGO</text>
  
  <!-- APP界面模拟 -->
  <rect x="500" y="150" width="250" height="450" class="app-bg"/>
  <rect x="520" y="170" width="210" height="40" fill="#c62828" rx="5"/>
  <text x="625" y="195" text-anchor="middle" class="content-text" fill="white" font-size="18px">招商银行APP</text>
  
  <!-- APP功能模块 -->
  <rect x="530" y="230" width="80" height="60" fill="#e3f2fd" rx="8"/>
  <text x="570" y="265" text-anchor="middle" class="content-text" font-size="14px">转账</text>
  
  <rect x="620" y="230" width="80" height="60" fill="#e8f5e8" rx="8"/>
  <text x="660" y="265" text-anchor="middle" class="content-text" font-size="14px">理财</text>
  
  <rect x="530" y="300" width="80" height="60" fill="#fff3e0" rx="8"/>
  <text x="570" y="335" text-anchor="middle" class="content-text" font-size="14px">饭票</text>
  
  <rect x="620" y="300" width="80" height="60" fill="#fce4ec" rx="8"/>
  <text x="660" y="335" text-anchor="middle" class="content-text" font-size="14px">影票</text>
  
  <!-- 引子问题 -->
  <rect x="800" y="150" width="1000" height="450" class="app-bg"/>
  <text x="850" y="220" class="subtitle">引子：</text>
  <text x="850" y="280" class="content-text">一个金融类APP，如何做到月活过亿，</text>
  <text x="850" y="330" class="content-text">成为用户手机里的"常客"？</text>
  
  <!-- 关键数据 -->
  <rect x="850" y="380" width="900" height="180" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="10"/>
  <text x="900" y="430" class="highlight-text">月活跃用户数：</text>
  <text x="1200" y="430" class="highlight-text" font-size="36px">1亿+</text>
  <text x="900" y="480" class="content-text">这几乎是一个商业奇迹！</text>
  <text x="900" y="520" class="content-text">它是怎么做到的？对我们有什么启发？</text>
  
  <!-- 底部思考区域 -->
  <rect x="150" y="650" width="1620" height="350" fill="#fff3cd" stroke="#ffc107" stroke-width="3" rx="15"/>
  <text x="200" y="720" class="subtitle">核心思考：</text>
  <text x="250" y="780" class="content-text">• 一个理论上非常低频的金融工具类APP</text>
  <text x="250" y="830" class="content-text">• 如何突破行业限制，实现高频使用？</text>
  <text x="250" y="880" class="content-text">• 它的成功经验，对我们运营中国移动APP有什么借鉴意义？</text>
  
  <!-- 装饰性元素 -->
  <circle cx="1700" cy="300" r="50" fill="#c62828" opacity="0.3"/>
  <text x="1700" y="310" text-anchor="middle" class="content-text" fill="#b71c1c" font-size="18px">学习</text>
  
  <circle cx="1700" cy="500" r="50" fill="#4caf50" opacity="0.3"/>
  <text x="1700" y="510" text-anchor="middle" class="content-text" fill="#2e7d32" font-size="18px">借鉴</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 150 150 Q 250 130 350 150" stroke="#c62828" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1620 150 Q 1720 130 1820 150" stroke="#c62828" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
