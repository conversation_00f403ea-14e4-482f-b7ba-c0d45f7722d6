<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; line-height: 1.6; }
      .item-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: bold; fill: #34495e; }
      .example-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 22px; fill: #7f8c8d; font-style: italic; }
      .checklist-bg { fill: #e8f5e8; stroke: #4caf50; stroke-width: 3; rx: 15; }
      .item-bg { fill: #f8f9fa; stroke: #e9ecef; stroke-width: 2; rx: 10; }
      .instruction-bg { fill: #fff3cd; stroke: #ffc107; stroke-width: 3; rx: 15; }
      .number-circle { fill: #2196f3; stroke: #1976d2; stroke-width: 2; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">【实战】打造你的专业IP"五件套"</text>
  
  <!-- 主要内容区域 -->
  <rect x="100" y="150" width="1720" height="750" class="checklist-bg"/>
  
  <!-- 清单标题 -->
  <text x="150" y="200" class="subtitle" fill="#2e7d32">专业IP打造清单：</text>
  
  <!-- 项目1：头像 -->
  <circle cx="200" cy="270" r="25" class="number-circle"/>
  <text x="200" y="278" text-anchor="middle" class="content-text" fill="white" font-weight="bold">1</text>
  
  <rect x="250" y="240" width="1520" height="80" class="item-bg"/>
  <text x="280" y="270" class="item-title">头像：专业、清晰、有亲和力的职业照</text>
  <text x="300" y="300" class="example-text">建议：正装或工装，微笑表情，高清画质，避免生活照或卡通头像</text>
  
  <!-- 项目2：昵称 -->
  <circle cx="200" cy="370" r="25" class="number-circle"/>
  <text x="200" y="378" text-anchor="middle" class="content-text" fill="white" font-weight="bold">2</text>
  
  <rect x="250" y="340" width="1520" height="80" class="item-bg"/>
  <text x="280" y="370" class="item-title">昵称："公司-角色-姓名"格式</text>
  <text x="300" y="400" class="example-text">示例：宝鸡移动-宽带专家-王刚 / 西安移动-客户经理-李小美</text>
  
  <!-- 项目3：个性签名 -->
  <circle cx="200" cy="470" r="25" class="number-circle"/>
  <text x="200" y="478" text-anchor="middle" class="content-text" fill="white" font-weight="bold">3</text>
  
  <rect x="250" y="440" width="1520" height="80" class="item-bg"/>
  <text x="280" y="470" class="item-title">个性签名：一句话体现你的价值</text>
  <text x="300" y="500" class="example-text">示例：通信有难题，随时找小李 / 您的专属通信管家，7×24小时在线</text>
  
  <!-- 项目4：朋友圈封面 -->
  <circle cx="200" cy="570" r="25" class="number-circle"/>
  <text x="200" y="578" text-anchor="middle" class="content-text" fill="white" font-weight="bold">4</text>
  
  <rect x="250" y="540" width="1520" height="80" class="item-bg"/>
  <text x="280" y="570" class="item-title">朋友圈封面：体现公司品牌或专业形象</text>
  <text x="300" y="600" class="example-text">建议：中国移动品牌元素、5G科技感图片、或个人专业形象照</text>
  
  <!-- 项目5：朋友圈内容 -->
  <circle cx="200" cy="670" r="25" class="number-circle"/>
  <text x="200" y="678" text-anchor="middle" class="content-text" fill="white" font-weight="bold">5</text>
  
  <rect x="250" y="640" width="1520" height="120" class="item-bg"/>
  <text x="280" y="670" class="item-title">朋友圈内容：遵循"4+1"原则</text>
  <text x="300" y="700" class="example-text">4条专业价值内容：通信知识、优惠活动、使用技巧、行业资讯</text>
  <text x="300" y="730" class="example-text">1条生活点滴内容：适度分享个人生活，增加亲和力和真实感</text>
  
  <!-- 现场练习指令 -->
  <rect x="200" y="800" width="1520" height="80" class="instruction-bg"/>
  <text x="250" y="830" class="subtitle" fill="#f57f17">现场练习指令：</text>
  <text x="250" y="860" class="content-text" fill="#f57f17">现场练习5分钟，立刻优化你的微信/企微形象！</text>
  
  <!-- 右侧示例展示 -->
  <rect x="1400" y="200" width="350" height="550" fill="#f1f2f6" stroke="#ddd" stroke-width="2" rx="15"/>
  <text x="1575" y="240" text-anchor="middle" class="content-text" font-weight="bold">优化前 VS 优化后</text>
  
  <!-- 优化前 -->
  <rect x="1420" y="260" width="150" height="120" fill="#ffebee" stroke="#e74c3c" stroke-width="1" rx="8"/>
  <text x="1495" y="285" text-anchor="middle" class="content-text" font-size="18px" font-weight="bold">优化前</text>
  <text x="1495" y="310" text-anchor="middle" class="content-text" font-size="16px">昵称：小王</text>
  <text x="1495" y="330" text-anchor="middle" class="content-text" font-size="16px">签名：无</text>
  <text x="1495" y="350" text-anchor="middle" class="content-text" font-size="16px">头像：生活照</text>
  
  <!-- 优化后 -->
  <rect x="1580" y="260" width="150" height="120" fill="#e8f5e8" stroke="#4caf50" stroke-width="1" rx="8"/>
  <text x="1655" y="285" text-anchor="middle" class="content-text" font-size="18px" font-weight="bold">优化后</text>
  <text x="1655" y="305" text-anchor="middle" class="content-text" font-size="14px">西安移动-王刚</text>
  <text x="1655" y="325" text-anchor="middle" class="content-text" font-size="14px">通信专家在线</text>
  <text x="1655" y="345" text-anchor="middle" class="content-text" font-size="14px">专业职业照</text>
  
  <!-- 效果对比 -->
  <text x="1575" y="420" text-anchor="middle" class="content-text" font-weight="bold">效果对比：</text>
  <text x="1430" y="450" class="content-text" font-size="20px">• 专业度提升</text>
  <text x="1430" y="480" class="content-text" font-size="20px">• 信任度增强</text>
  <text x="1430" y="510" class="content-text" font-size="20px">• 识别度提高</text>
  <text x="1430" y="540" class="content-text" font-size="20px">• 转化率上升</text>
  
  <!-- 装饰性元素 -->
  <circle cx="50" cy="450" r="30" fill="#4caf50" opacity="0.3"/>
  <text x="50" y="460" text-anchor="middle" class="content-text" fill="#2e7d32" font-size="16px">IP</text>
  
  <circle cx="1870" cy="450" r="30" fill="#2196f3" opacity="0.3"/>
  <text x="1870" y="460" text-anchor="middle" class="content-text" fill="#1565c0" font-size="16px">专业</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 150 Q 200 130 300 150" stroke="#4caf50" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1620 150 Q 1720 130 1820 150" stroke="#2196f3" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
