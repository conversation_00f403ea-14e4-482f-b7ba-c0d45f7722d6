<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #2c3e50; line-height: 1.6; }
      .problem-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #e74c3c; line-height: 1.5; }
      .solution-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #4caf50; line-height: 1.5; }
      .detective-bg { fill: #f8f9fa; stroke: #e9ecef; stroke-width: 3; rx: 20; }
      .problem-bg { fill: #ffebee; stroke: #e74c3c; stroke-width: 3; rx: 15; }
      .solution-bg { fill: #e8f5e8; stroke: #4caf50; stroke-width: 3; rx: 15; }
      .magnifier { fill: #e74c3c; opacity: 0.8; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">从"结果"到"过程"：我们需要一个"侦察工具"</text>
  
  <!-- 主要内容区域 -->
  <rect x="100" y="150" width="1720" height="750" class="detective-bg"/>
  
  <!-- 侦探图标区域 -->
  <rect x="200" y="200" width="400" height="600" fill="#f1f2f6" stroke="#ddd" stroke-width="2" rx="15"/>
  
  <!-- 侦探剪影 -->
  <circle cx="400" cy="350" r="60" fill="#34495e"/>
  <rect x="360" y="410" width="80" height="120" fill="#34495e" rx="10"/>
  
  <!-- 放大镜 -->
  <circle cx="500" cy="300" r="50" class="magnifier"/>
  <circle cx="500" cy="300" r="35" fill="none" stroke="white" stroke-width="6"/>
  <line x1="530" y1="330" x2="570" y2="370" stroke="white" stroke-width="8" stroke-linecap="round"/>
  
  <!-- 地图 -->
  <rect x="250" y="550" width="300" height="200" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2" rx="10"/>
  <path d="M 270 570 Q 350 560 430 570 Q 500 580 530 570" stroke="#3498db" stroke-width="3" fill="none"/>
  <path d="M 270 620 Q 350 610 430 620 Q 500 630 530 620" stroke="#e74c3c" stroke-width="3" fill="none"/>
  <circle cx="300" cy="590" r="5" fill="#e74c3c"/>
  <circle cx="400" cy="600" r="5" fill="#4caf50"/>
  <circle cx="500" cy="590" r="5" fill="#f39c12"/>
  
  <text x="400" y="740" text-anchor="middle" class="content-text" font-weight="bold" fill="#34495e">侦察工具</text>
  
  <!-- 问题描述 -->
  <rect x="650" y="200" width="1020" height="200" class="problem-bg"/>
  <text x="700" y="250" class="subtitle" fill="#c62828">问题：</text>
  <text x="700" y="290" class="problem-text">NPS告诉我们客户"爽不爽"，</text>
  <text x="700" y="330" class="problem-text">但没有告诉我们，他到底是在哪个环节"不爽"的。</text>
  <text x="700" y="370" class="problem-text">我们需要看清服务的全过程！</text>
  
  <!-- 解决方案 -->
  <rect x="650" y="430" width="1020" height="200" class="solution-bg"/>
  <text x="700" y="480" class="subtitle" fill="#2e7d32">解决方案：</text>
  <text x="700" y="520" class="solution-text">我们需要一个能让我们"魂穿"到客户身上，</text>
  <text x="700" y="560" class="solution-text">看清服务全过程的"侦察工具"。</text>
  
  <!-- 引出客户旅程地图 -->
  <rect x="650" y="660" width="1020" height="140" fill="#fff3cd" stroke="#ffc107" stroke-width="3" rx="15"/>
  <text x="700" y="710" class="subtitle" fill="#f57f17">引出：</text>
  <text x="700" y="750" class="content-text" font-weight="bold" fill="#f57f17">客户旅程地图</text>
  <text x="700" y="780" class="content-text" fill="#f57f17">(Customer Journey Map)</text>
  
  <!-- 装饰性元素 -->
  <circle cx="50" cy="500" r="30" fill="#e74c3c" opacity="0.3"/>
  <text x="50" y="510" text-anchor="middle" class="content-text" fill="#c62828" font-size="16px">结果</text>
  
  <circle cx="1870" cy="500" r="30" fill="#4caf50" opacity="0.3"/>
  <text x="1870" y="510" text-anchor="middle" class="content-text" fill="#2e7d32" font-size="16px">过程</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 150 Q 200 130 300 150" stroke="#e74c3c" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1620 150 Q 1720 130 1820 150" stroke="#4caf50" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
