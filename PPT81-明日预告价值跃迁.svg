<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .day-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #2c3e50; line-height: 1.6; }
      .highlight-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #e74c3c; line-height: 1.5; }
      .bottom-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; fill: #2c3e50; font-weight: bold; }
      .preview-bg { fill: #fff3e0; stroke: #ff9800; stroke-width: 3; rx: 20; }
      .rocket { fill: #e74c3c; opacity: 0.8; }
      .flame { fill: #ff9800; opacity: 0.6; }
    </style>
    <defs>
      <radialGradient id="sunrise-gradient" cx="50%" cy="80%" r="60%">
        <stop offset="0%" style="stop-color:#ff9800;stop-opacity:0.8" />
        <stop offset="100%" style="stop-color:#fff3e0;stop-opacity:0.3" />
      </radialGradient>
    </defs>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 旭日东升背景 -->
  <ellipse cx="960" cy="800" rx="800" ry="300" fill="url(#sunrise-gradient)"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" class="title">明日预告</text>
  
  <!-- 主要内容区域 -->
  <rect x="200" y="200" width="1520" height="650" class="preview-bg"/>
  
  <!-- Day 3 标题 -->
  <text x="960" y="300" text-anchor="middle" class="day-title">Day 3: 价值跃迁</text>
  
  <!-- 火箭图标 -->
  <rect x="400" y="350" width="40" height="120" class="rocket" rx="20"/>
  <polygon points="400,350 420,320 440,350" class="rocket"/>
  <rect x="410" y="470" width="20" height="40" class="flame"/>
  <polygon points="405,510 420,530 435,510" class="flame"/>
  
  <rect x="1480" y="350" width="40" height="120" class="rocket" rx="20"/>
  <polygon points="1480,350 1500,320 1520,350" class="rocket"/>
  <rect x="1490" y="470" width="20" height="40" class="flame"/>
  <polygon points="1485,510 1500,530 1515,510" class="flame"/>
  
  <!-- 核心看点 -->
  <text x="960" y="400" text-anchor="middle" class="content-text" font-weight="bold">核心看点：</text>
  
  <text x="300" y="460" class="highlight-text">• 如何用数据驱动，让营销效果翻倍？</text>
  
  <text x="300" y="510" class="highlight-text">• 如何科学地挽留即将流失的高价值客户？</text>
  
  <text x="300" y="560" class="highlight-text">• 【终极实战】：现场设计一份可向领导汇报的运营方案！</text>
  
  <!-- 底部文字 -->
  <text x="960" y="700" text-anchor="middle" class="bottom-text">最后一程，让我们将所有努力，</text>
  <text x="960" y="750" text-anchor="middle" class="bottom-text">转化为看得见的价值！</text>
  
  <!-- 装饰性数据图表 -->
  <rect x="600" y="350" width="200" height="120" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" rx="10" opacity="0.7"/>
  <text x="700" y="385" text-anchor="middle" class="content-text" fill="#1565c0" font-size="20px">数据驱动</text>
  <rect x="620" y="400" width="30" height="50" fill="#2196f3" opacity="0.6"/>
  <rect x="660" y="420" width="30" height="30" fill="#2196f3" opacity="0.6"/>
  <rect x="700" y="410" width="30" height="40" fill="#2196f3" opacity="0.6"/>
  <rect x="740" y="390" width="30" height="60" fill="#2196f3" opacity="0.6"/>
  
  <rect x="1120" y="350" width="200" height="120" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="10" opacity="0.7"/>
  <text x="1220" y="385" text-anchor="middle" class="content-text" fill="#2e7d32" font-size="20px">价值增长</text>
  <path d="M 1140 440 Q 1180 420 1220 400 Q 1260 380 1300 360" stroke="#4caf50" stroke-width="4" fill="none"/>
  <circle cx="1300" cy="360" r="5" fill="#4caf50"/>
  
  <!-- 装饰性元素 -->
  <circle cx="100" cy="500" r="40" fill="#ff9800" opacity="0.3"/>
  <text x="100" y="510" text-anchor="middle" class="content-text" fill="#e65100" font-size="18px">明日</text>
  
  <circle cx="1820" cy="500" r="40" fill="#e74c3c" opacity="0.3"/>
  <text x="1820" y="510" text-anchor="middle" class="content-text" fill="#c62828" font-size="18px">跃迁</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 200 200 Q 300 180 400 200" stroke="#ff9800" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1520 200 Q 1620 180 1720 200" stroke="#e74c3c" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
