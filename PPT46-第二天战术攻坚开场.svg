<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .main-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #e74c3c; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; fill: #2c3e50; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #34495e; line-height: 1.6; }
      .day-bg { fill: #fff3cd; stroke: #ffc107; stroke-width: 4; rx: 20; }
      .topic-bg { fill: #e8f5e8; stroke: #4caf50; stroke-width: 3; rx: 15; }
      .mountain-bg { fill: #ecf0f1; stroke: #bdc3c7; stroke-width: 2; }
    </style>
    <defs>
      <linearGradient id="sunrise-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#ff9800;stop-opacity:0.8" />
        <stop offset="50%" style="stop-color:#ffc107;stop-opacity:0.6" />
        <stop offset="100%" style="stop-color:#fff3e0;stop-opacity:0.4" />
      </linearGradient>
    </defs>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 背景山峰 -->
  <polygon points="0,600 300,300 600,400 900,250 1200,350 1500,200 1800,300 1920,400 1920,1080 0,1080" class="mountain-bg"/>
  
  <!-- 日出效果 -->
  <circle cx="1600" cy="300" r="120" fill="url(#sunrise-gradient)"/>
  
  <!-- 主标题区域 -->
  <rect x="200" y="150" width="1520" height="200" class="day-bg"/>
  <text x="960" y="230" text-anchor="middle" class="main-title">Day 2: 战术攻坚</text>
  <text x="960" y="290" text-anchor="middle" class="subtitle">从战略蓝图到一线实战，打赢客户经营的关键战役</text>
  
  <!-- 核心议题区域 -->
  <rect x="200" y="400" width="1520" height="400" class="topic-bg"/>
  <text x="250" y="460" class="subtitle" fill="#2e7d32">本日核心议题：</text>
  
  <text x="300" y="520" class="content-text">• 全渠道协同运营</text>
  <text x="300" y="570" class="content-text">• 中国移动APP精细化促活</text>
  <text x="300" y="620" class="content-text">• 企业微信私域深耕</text>
  <text x="300" y="670" class="content-text">• 客户旅程地图实战</text>
  
  <!-- 装饰性图标 -->
  <circle cx="1600" cy="520" r="50" fill="#e74c3c" opacity="0.3"/>
  <text x="1600" y="530" text-anchor="middle" class="content-text" fill="#c62828" font-size="20px">战术</text>
  
  <circle cx="1600" cy="620" r="50" fill="#4caf50" opacity="0.3"/>
  <text x="1600" y="630" text-anchor="middle" class="content-text" fill="#2e7d32" font-size="20px">实战</text>
  
  <circle cx="1600" cy="720" r="50" fill="#2196f3" opacity="0.3"/>
  <text x="1600" y="730" text-anchor="middle" class="content-text" fill="#1565c0" font-size="20px">攻坚</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 200 150 Q 300 130 400 150" stroke="#ffc107" stroke-width="4" fill="none" opacity="0.7"/>
  <path d="M 1520 150 Q 1620 130 1720 150" stroke="#ffc107" stroke-width="4" fill="none" opacity="0.7"/>
  
  <path d="M 200 400 Q 300 380 400 400" stroke="#4caf50" stroke-width="4" fill="none" opacity="0.7"/>
  <path d="M 1520 400 Q 1620 380 1720 400" stroke="#4caf50" stroke-width="4" fill="none" opacity="0.7"/>
</svg>
