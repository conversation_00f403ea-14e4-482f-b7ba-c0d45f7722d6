<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <text x="960" y="100" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="#003d7a">让客户"活起来"的终极工具：用户画像 (Persona)</text>
  
  <!-- 人像轮廓从模糊到清晰 -->
  <g transform="translate(300, 300)">
    <!-- 模糊轮廓 -->
    <circle cx="0" cy="0" r="60" fill="#cccccc" opacity="0.3"/>
    <ellipse cx="0" cy="80" rx="50" ry="70" fill="#cccccc" opacity="0.3"/>
    
    <!-- 逐渐清晰的轮廓 -->
    <circle cx="0" cy="0" r="55" fill="#999999" opacity="0.5"/>
    <ellipse cx="0" cy="75" rx="45" ry="65" fill="#999999" opacity="0.5"/>
    
    <!-- 最清晰的轮廓 -->
    <circle cx="0" cy="0" r="50" fill="#666666" opacity="0.7"/>
    <ellipse cx="0" cy="70" rx="40" ry="60" fill="#666666" opacity="0.7"/>
    
    <!-- 面部特征 -->
    <circle cx="-15" cy="-10" r="5" fill="#333"/>
    <circle cx="15" cy="-10" r="5" fill="#333"/>
    <path d="M -10 10 Q 0 20 10 10" stroke="#333" stroke-width="3" fill="none"/>
  </g>
  
  <!-- 核心定义 -->
  <rect x="500" y="200" width="1200" height="200" rx="20" fill="#f0f8ff" stroke="#0066cc" stroke-width="3"/>
  <text x="1100" y="240" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#0066cc">核心定义</text>
  
  <text x="550" y="280" font-family="Microsoft YaHei" font-size="24" fill="#003d7a">用户画像（Persona），不是一个真实的用户，而是我们基于对某一类客户群体的</text>
  <text x="550" y="315" font-family="Microsoft YaHei" font-size="24" fill="#003d7a">深刻洞察，共同创造出来的一个虚拟的、典型的"客户代言人"。</text>
  
  <text x="550" y="360" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#ff6600">我们要给他取一个真实的名字，为他选一张看起来很像他的照片，</text>
  <text x="550" y="385" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#ff6600">甚至为他编一段有血有肉的故事。</text>
  
  <!-- 价值说明 -->
  <text x="960" y="480" text-anchor="middle" font-family="Microsoft YaHei" font-size="42" font-weight="bold" fill="#003d7a">它的价值：</text>
  
  <!-- 价值卡片 -->
  <rect x="150" y="520" width="500" height="180" rx="15" fill="#ffe6e6" stroke="#cc3333" stroke-width="2"/>
  <text x="400" y="560" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#cc3333">启动同理心</text>
  <text x="200" y="600" font-family="Microsoft YaHei" font-size="22" fill="#333">让我们从"管理数据"</text>
  <text x="200" y="630" font-family="Microsoft YaHei" font-size="22" fill="#333">转向"服务一个具体的人"。</text>
  <text x="200" y="670" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#ff6600">有温度、有情感</text>
  
  <rect x="710" y="520" width="500" height="180" rx="15" fill="#e6ffe6" stroke="#009900" stroke-width="2"/>
  <text x="960" y="560" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#009900">统一团队语言</text>
  <text x="760" y="600" font-family="Microsoft YaHei" font-size="22" fill="#333">让开发、市场、服务团队，</text>
  <text x="760" y="630" font-family="Microsoft YaHei" font-size="22" fill="#333">心中装着同一个"他/她"。</text>
  <text x="760" y="670" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#ff6600">协同一致</text>
  
  <rect x="1270" y="520" width="500" height="180" rx="15" fill="#e6f3ff" stroke="#0066cc" stroke-width="2"/>
  <text x="1520" y="560" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#0066cc">驱动产品决策</text>
  <text x="1320" y="600" font-family="Microsoft YaHei" font-size="22" fill="#333">在做任何决策时，反问自己：</text>
  <text x="1320" y="630" font-family="Microsoft YaHei" font-size="22" fill="#333">"'他/她'会喜欢这个功能吗？"</text>
  <text x="1320" y="670" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#ff6600">用户导向</text>
  
  <!-- 底部总结 -->
  <rect x="200" y="750" width="1520" height="200" rx="20" fill="#fff5e6" stroke="#ff9900" stroke-width="3"/>
  <text x="960" y="800" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#ff6600">从此以后，我们整个团队</text>
  <text x="960" y="840" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" fill="#003d7a">在设计产品、策划营销、优化服务的时候，</text>
  <text x="960" y="880" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" fill="#003d7a">心里都装着这个具体的人，为他服务，</text>
  <text x="960" y="920" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#009900">而不是为一堆模糊的数字服务。</text>
  
  <!-- 装饰弧线 -->
  <path d="M 200 1000 Q 960 970 1720 1000" stroke="#0066cc" stroke-width="3" fill="none" opacity="0.5"/>
</svg>
