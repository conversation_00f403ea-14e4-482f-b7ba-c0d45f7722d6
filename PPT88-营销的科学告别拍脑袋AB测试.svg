<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #2c3e50; line-height: 1.6; }
      .principle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; line-height: 1.5; }
      .main-bg { fill: #f8f9fa; stroke: #e9ecef; stroke-width: 3; rx: 20; }
      .core-bg { fill: #fff3cd; stroke: #ffc107; stroke-width: 3; rx: 15; }
      .principle-bg { fill: #e3f2fd; stroke: #2196f3; stroke-width: 3; rx: 15; }
      .balance { fill: #8d6e63; opacity: 0.8; }
      .scale-a { fill: #e74c3c; opacity: 0.8; }
      .scale-b { fill: #4caf50; opacity: 0.8; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">营销的科学：告别"拍脑袋"的A/B测试</text>
  
  <!-- 主要内容区域 -->
  <rect x="100" y="150" width="1720" height="750" class="main-bg"/>
  
  <!-- 天平图标 -->
  <rect x="400" y="200" width="400" height="300" fill="#f1f2f6" stroke="#ddd" stroke-width="2" rx="15"/>
  
  <!-- 天平支架 -->
  <rect x="595" y="250" width="10" height="200" class="balance"/>
  <circle cx="600" cy="350" r="15" class="balance"/>
  
  <!-- 天平横梁 -->
  <rect x="500" y="345" width="200" height="10" class="balance" rx="5"/>
  
  <!-- 左侧秤盘 A -->
  <rect x="480" y="355" width="60" height="8" class="scale-a" rx="4"/>
  <text x="510" y="380" text-anchor="middle" class="content-text" fill="#e74c3c" font-size="24px">A</text>
  
  <!-- 右侧秤盘 B -->
  <rect x="660" y="345" width="60" height="8" class="scale-b" rx="4"/>
  <text x="690" y="370" text-anchor="middle" class="content-text" fill="#4caf50" font-size="24px">B</text>
  
  <!-- 核心思想 -->
  <rect x="850" y="200" width="770" height="300" class="core-bg"/>
  <text x="900" y="240" class="subtitle" fill="#f57f17">核心思想：</text>
  
  <text x="900" y="290" class="content-text">当你不确定A方案和B方案哪个更好时，</text>
  <text x="900" y="330" class="content-text">别争论，别猜测，</text>
  <text x="900" y="370" class="content-text">让用户用真实的行为数据</text>
  <text x="900" y="410" class="content-text">来告诉你答案。</text>
  
  <!-- 基本原理 -->
  <rect x="200" y="550" width="1520" height="300" class="principle-bg"/>
  <text x="250" y="590" class="subtitle" fill="#1565c0">基本原理：</text>
  
  <!-- 对照组 -->
  <rect x="300" y="620" width="400" height="120" fill="#ffebee" stroke="#e74c3c" stroke-width="2" rx="10"/>
  <text x="320" y="650" class="principle-text" font-weight="bold" fill="#c62828">对照组 (A)：使用原有方案</text>
  <text x="320" y="680" class="principle-text" fill="#c62828">保持现状，作为基准对比</text>
  <text x="320" y="710" class="principle-text" fill="#c62828">验证现有方案的效果</text>
  
  <!-- 实验组 -->
  <rect x="750" y="620" width="400" height="120" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="10"/>
  <text x="770" y="650" class="principle-text" font-weight="bold" fill="#2e7d32">实验组 (B)：使用新方案</text>
  <text x="770" y="680" class="principle-text" fill="#2e7d32">测试新的创意或策略</text>
  <text x="770" y="710" class="principle-text" fill="#2e7d32">探索优化的可能性</text>
  
  <!-- 方法说明 -->
  <rect x="1200" y="620" width="420" height="120" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2" rx="10"/>
  <text x="1220" y="650" class="principle-text" font-weight="bold" fill="#7b1fa2">方法：</text>
  <text x="1220" y="680" class="principle-text" fill="#7b1fa2">随机将用户分成两组，</text>
  <text x="1220" y="710" class="principle-text" fill="#7b1fa2">看哪个方案效果更好</text>
  
  <!-- 核心价值 -->
  <rect x="250" y="770" width="1420" height="80" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="10"/>
  <text x="300" y="800" class="content-text" font-weight="bold" fill="#2e7d32">核心价值：</text>
  <text x="300" y="830" class="content-text" fill="#2e7d32">用最小成本，找到最优解的强大武器 - 科学决策，告别"老经验"和"拍脑袋"</text>
  
  <!-- 装饰性元素 -->
  <circle cx="50" cy="500" r="30" fill="#e74c3c" opacity="0.3"/>
  <text x="50" y="510" text-anchor="middle" class="content-text" fill="#c62828" font-size="16px">A/B</text>
  
  <circle cx="1870" cy="500" r="30" fill="#4caf50" opacity="0.3"/>
  <text x="1870" y="510" text-anchor="middle" class="content-text" fill="#2e7d32" font-size="16px">科学</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 150 Q 200 130 300 150" stroke="#e74c3c" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1620 150 Q 1720 130 1820 150" stroke="#4caf50" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
