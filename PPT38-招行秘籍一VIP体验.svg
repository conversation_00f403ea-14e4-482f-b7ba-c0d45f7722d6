<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; line-height: 1.6; }
      .phone-bg { fill: #f8f9fa; stroke: #333; stroke-width: 3; rx: 25; }
      .screen-bg { fill: #ffffff; stroke: #ddd; stroke-width: 1; rx: 15; }
      .ordinary-bg { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; rx: 10; }
      .vip-bg { fill: #fff3e0; stroke: #ff9800; stroke-width: 2; rx: 10; }
      .conclusion-bg { fill: #e8f5e8; stroke: #4caf50; stroke-width: 3; rx: 15; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">招行秘籍一：因人而异的"VIP"体验</text>
  
  <!-- 核心打法 -->
  <text x="960" y="150" text-anchor="middle" class="subtitle">核心打法：极致的客户分层运营</text>
  
  <!-- 左侧：普通用户界面 -->
  <text x="400" y="220" text-anchor="middle" class="content-text" font-weight="bold">普通用户APP首页</text>
  
  <rect x="250" y="250" width="300" height="500" class="phone-bg"/>
  <rect x="270" y="280" width="260" height="440" class="screen-bg"/>
  
  <!-- 普通用户功能 -->
  <rect x="290" y="300" width="220" height="80" class="ordinary-bg"/>
  <text x="400" y="350" text-anchor="middle" class="content-text" font-size="20px">转账汇款</text>
  
  <rect x="290" y="390" width="220" height="80" class="ordinary-bg"/>
  <text x="400" y="440" text-anchor="middle" class="content-text" font-size="20px">账户查询</text>
  
  <rect x="290" y="480" width="220" height="80" class="ordinary-bg"/>
  <text x="400" y="530" text-anchor="middle" class="content-text" font-size="20px">基础理财</text>
  
  <rect x="290" y="570" width="220" height="80" class="ordinary-bg"/>
  <text x="400" y="620" text-anchor="middle" class="content-text" font-size="20px">生活缴费</text>
  
  <!-- 右侧：金葵花用户界面 -->
  <text x="1400" y="220" text-anchor="middle" class="content-text" font-weight="bold">金葵花用户APP首页</text>
  
  <rect x="1250" y="250" width="300" height="500" class="phone-bg"/>
  <rect x="1270" y="280" width="260" height="440" class="screen-bg"/>
  
  <!-- VIP用户功能 -->
  <rect x="1290" y="300" width="220" height="80" class="vip-bg"/>
  <text x="1400" y="350" text-anchor="middle" class="content-text" font-size="18px">专属理财顾问</text>
  
  <rect x="1290" y="390" width="220" height="80" class="vip-bg"/>
  <text x="1400" y="440" text-anchor="middle" class="content-text" font-size="18px">高端理财产品</text>
  
  <rect x="1290" y="480" width="220" height="80" class="vip-bg"/>
  <text x="1400" y="530" text-anchor="middle" class="content-text" font-size="18px">增值服务预约</text>
  
  <rect x="1290" y="570" width="220" height="80" class="vip-bg"/>
  <text x="1400" y="620" text-anchor="middle" class="content-text" font-size="18px">私人银行服务</text>
  
  <!-- 中间对比箭头 -->
  <text x="960" y="400" text-anchor="middle" class="subtitle" font-size="48px">VS</text>
  <text x="960" y="450" text-anchor="middle" class="content-text">差异化界面</text>
  <text x="960" y="480" text-anchor="middle" class="content-text">差异化服务</text>
  
  <!-- 结论区域 -->
  <rect x="150" y="800" width="1620" height="200" class="conclusion-bg"/>
  <text x="200" y="850" class="subtitle">结论：</text>
  <text x="200" y="900" class="content-text">通过差异化的服务和界面，让高价值客户感受到"被偏爱"的尊贵感。</text>
  <text x="200" y="940" class="content-text">不同价值的客户，看到完全不同的产品推荐、功能入口和服务体验。</text>
  
  <!-- 装饰性元素 -->
  <circle cx="100" cy="400" r="40" fill="#2196f3" opacity="0.3"/>
  <text x="100" y="410" text-anchor="middle" class="content-text" fill="#1565c0" font-size="16px">普通</text>
  
  <circle cx="1820" cy="400" r="40" fill="#ff9800" opacity="0.3"/>
  <text x="1820" y="410" text-anchor="middle" class="content-text" fill="#e65100" font-size="16px">VIP</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 250 250 Q 350 230 450 250" stroke="#2196f3" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1350 250 Q 1450 230 1550 250" stroke="#ff9800" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
