<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .header-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; fill: #34495e; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 22px; fill: #2c3e50; line-height: 1.6; }
      .tip-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; fill: #7f8c8d; }
      .canvas-bg { fill: #f8f9fa; stroke: #e9ecef; stroke-width: 3; rx: 20; }
      .table-header { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; }
      .table-cell { fill: #ffffff; stroke: #ddd; stroke-width: 1; }
      .tip-bg { fill: #fff3cd; stroke: #ffc107; stroke-width: 2; rx: 10; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">客户旅程地图画布（参考）</text>
  
  <!-- 主要内容区域 -->
  <rect x="100" y="150" width="1720" height="750" class="canvas-bg"/>
  
  <!-- 表格结构 -->
  <!-- 表头 -->
  <rect x="150" y="200" width="200" height="60" class="table-header"/>
  <text x="250" y="235" text-anchor="middle" class="header-text" fill="#1565c0">要素</text>
  
  <rect x="350" y="200" width="200" height="60" class="table-header"/>
  <text x="450" y="235" text-anchor="middle" class="header-text" fill="#1565c0">阶段1</text>
  
  <rect x="550" y="200" width="200" height="60" class="table-header"/>
  <text x="650" y="235" text-anchor="middle" class="header-text" fill="#1565c0">阶段2</text>
  
  <rect x="750" y="200" width="200" height="60" class="table-header"/>
  <text x="850" y="235" text-anchor="middle" class="header-text" fill="#1565c0">阶段3</text>
  
  <rect x="950" y="200" width="200" height="60" class="table-header"/>
  <text x="1050" y="235" text-anchor="middle" class="header-text" fill="#1565c0">阶段4</text>
  
  <rect x="1150" y="200" width="200" height="60" class="table-header"/>
  <text x="1250" y="235" text-anchor="middle" class="header-text" fill="#1565c0">阶段5</text>
  
  <rect x="1350" y="200" width="200" height="60" class="table-header"/>
  <text x="1450" y="235" text-anchor="middle" class="header-text" fill="#1565c0">阶段6</text>
  
  <rect x="1550" y="200" width="200" height="60" class="table-header"/>
  <text x="1650" y="235" text-anchor="middle" class="header-text" fill="#1565c0">阶段7</text>
  
  <!-- 行标签和内容 -->
  <!-- 阶段 -->
  <rect x="150" y="260" width="200" height="60" class="table-header"/>
  <text x="250" y="295" text-anchor="middle" class="header-text" fill="#1565c0">阶段 (Stage)</text>
  
  <!-- 用户行为 -->
  <rect x="150" y="320" width="200" height="60" class="table-header"/>
  <text x="250" y="355" text-anchor="middle" class="header-text" fill="#1565c0">用户行为 (Actions)</text>
  
  <!-- 服务触点 -->
  <rect x="150" y="380" width="200" height="60" class="table-header"/>
  <text x="250" y="415" text-anchor="middle" class="header-text" fill="#1565c0">服务触点 (Touchpoints)</text>
  
  <!-- 用户想法 -->
  <rect x="150" y="440" width="200" height="60" class="table-header"/>
  <text x="250" y="475" text-anchor="middle" class="header-text" fill="#1565c0">用户想法 (Thoughts)</text>
  
  <!-- 情绪曲线 -->
  <rect x="150" y="500" width="200" height="60" class="table-header"/>
  <text x="250" y="535" text-anchor="middle" class="header-text" fill="#1565c0">情绪曲线 (Feelings)</text>
  
  <!-- 痛点 -->
  <rect x="150" y="560" width="200" height="60" class="table-header"/>
  <text x="250" y="595" text-anchor="middle" class="header-text" fill="#1565c0">痛点 (Pain Points)</text>
  
  <!-- 机会点 -->
  <rect x="150" y="620" width="200" height="60" class="table-header"/>
  <text x="250" y="655" text-anchor="middle" class="header-text" fill="#1565c0">机会点 (Opportunities)</text>
  
  <!-- 空白单元格 -->
  <rect x="350" y="260" width="1400" height="420" class="table-cell"/>
  
  <!-- 网格线 -->
  <line x1="550" y1="260" x2="550" y2="680" stroke="#ddd" stroke-width="1"/>
  <line x1="750" y1="260" x2="750" y2="680" stroke="#ddd" stroke-width="1"/>
  <line x1="950" y1="260" x2="950" y2="680" stroke="#ddd" stroke-width="1"/>
  <line x1="1150" y1="260" x2="1150" y2="680" stroke="#ddd" stroke-width="1"/>
  <line x1="1350" y1="260" x2="1350" y2="680" stroke="#ddd" stroke-width="1"/>
  <line x1="1550" y1="260" x2="1550" y2="680" stroke="#ddd" stroke-width="1"/>
  
  <line x1="350" y1="320" x2="1750" y2="320" stroke="#ddd" stroke-width="1"/>
  <line x1="350" y1="380" x2="1750" y2="380" stroke="#ddd" stroke-width="1"/>
  <line x1="350" y1="440" x2="1750" y2="440" stroke="#ddd" stroke-width="1"/>
  <line x1="350" y1="500" x2="1750" y2="500" stroke="#ddd" stroke-width="1"/>
  <line x1="350" y1="560" x2="1750" y2="560" stroke="#ddd" stroke-width="1"/>
  <line x1="350" y1="620" x2="1750" y2="620" stroke="#ddd" stroke-width="1"/>
  
  <!-- 底部提示 -->
  <rect x="200" y="720" width="1520" height="120" class="tip-bg"/>
  <text x="250" y="760" class="content-text" font-weight="bold" fill="#f57f17">底部提示：</text>
  <text x="250" y="790" class="tip-text">善用不同颜色的便利贴，让地图更清晰！</text>
  <text x="250" y="820" class="tip-text">• 蓝色便利贴：用户行为和触点  • 黄色便利贴：用户想法和情绪</text>
  <text x="250" y="850" class="tip-text">• 红色便利贴：痛点标记  • 绿色便利贴：机会点和优化建议</text>
  
  <!-- 装饰性元素 -->
  <circle cx="50" cy="500" r="30" fill="#2196f3" opacity="0.3"/>
  <text x="50" y="510" text-anchor="middle" class="content-text" fill="#1565c0" font-size="16px">画布</text>
  
  <circle cx="1870" cy="500" r="30" fill="#ffc107" opacity="0.3"/>
  <text x="1870" y="510" text-anchor="middle" class="content-text" fill="#f57f17" font-size="16px">便利贴</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 150 Q 200 130 300 150" stroke="#2196f3" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1620 150 Q 1720 130 1820 150" stroke="#ffc107" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
