<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .day-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #e74c3c; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #34495e; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #2c3e50; line-height: 1.6; }
      .preview-bg { fill: #e8f5e8; stroke: #4caf50; stroke-width: 3; rx: 20; }
      .icon-bg { fill: #f8f9fa; stroke: #e9ecef; stroke-width: 2; rx: 15; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">明日预告</text>
  
  <!-- Day 2 标题 -->
  <text x="960" y="180" text-anchor="middle" class="day-title">Day 2: 战术攻坚</text>
  
  <!-- 预告内容区域 -->
  <rect x="150" y="230" width="1620" height="600" class="preview-bg"/>
  
  <text x="200" y="300" class="subtitle">核心看点：</text>
  
  <!-- 看点1 -->
  <rect x="250" y="340" width="100" height="80" class="icon-bg"/>
  <text x="300" y="385" text-anchor="middle" class="content-text" font-size="20px">APP</text>
  <text x="400" y="380" class="content-text">• 中国移动APP运营的"顶级心法"</text>
  
  <!-- 看点2 -->
  <rect x="250" y="450" width="100" height="80" class="icon-bg"/>
  <text x="300" y="495" text-anchor="middle" class="content-text" font-size="18px">企微</text>
  <text x="400" y="490" class="content-text">• 从0到1玩转企业微信私域流量</text>
  
  <!-- 看点3 -->
  <rect x="250" y="560" width="100" height="80" class="icon-bg"/>
  <text x="300" y="605" text-anchor="middle" class="content-text" font-size="18px">地图</text>
  <text x="400" y="600" class="content-text">• "读心术"级工具：客户旅程地图实战</text>
  
  <!-- 酷炫图标区域 -->
  <rect x="1200" y="340" width="500" height="300" fill="#f1f2f6" stroke="#ddd" stroke-width="2" rx="15"/>
  
  <!-- APP图标 -->
  <rect x="1250" y="380" width="80" height="80" fill="#2196f3" rx="15"/>
  <text x="1290" y="430" text-anchor="middle" class="content-text" fill="white" font-size="20px">APP</text>
  
  <!-- 企微图标 -->
  <rect x="1370" y="380" width="80" height="80" fill="#4caf50" rx="15"/>
  <text x="1410" y="430" text-anchor="middle" class="content-text" fill="white" font-size="18px">企微</text>
  
  <!-- 地图图标 -->
  <rect x="1490" y="380" width="80" height="80" fill="#ff9800" rx="15"/>
  <text x="1530" y="430" text-anchor="middle" class="content-text" fill="white" font-size="18px">地图</text>
  
  <!-- 连接线 -->
  <path d="M 1290 460 Q 1350 480 1410 460" stroke="#7f8c8d" stroke-width="3" fill="none"/>
  <path d="M 1410 460 Q 1470 480 1530 460" stroke="#7f8c8d" stroke-width="3" fill="none"/>
  
  <text x="1450" y="520" text-anchor="middle" class="content-text" font-weight="bold">战术工具箱</text>
  
  <!-- 底部激励文字 -->
  <rect x="200" y="700" width="1520" height="100" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="10"/>
  <text x="960" y="740" text-anchor="middle" class="subtitle" fill="#f57f17">从"战略"走向"战术"</text>
  <text x="960" y="780" text-anchor="middle" class="content-text" fill="#f57f17">更精彩、更实战的"战术攻坚篇"</text>
  
  <!-- 装饰性元素 -->
  <circle cx="100" cy="400" r="40" fill="#e74c3c" opacity="0.3"/>
  <text x="100" y="410" text-anchor="middle" class="content-text" fill="#c62828" font-size="18px">明日</text>
  
  <circle cx="1820" cy="400" r="40" fill="#4caf50" opacity="0.3"/>
  <text x="1820" y="410" text-anchor="middle" class="content-text" fill="#2e7d32" font-size="18px">实战</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 150 230 Q 250 210 350 230" stroke="#4caf50" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1570 230 Q 1670 210 1770 230" stroke="#4caf50" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
