<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #2c3e50; line-height: 1.6; }
      .feature-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 22px; fill: #2c3e50; line-height: 1.5; }
      .public-bg { fill: #e3f2fd; stroke: #2196f3; stroke-width: 3; rx: 20; }
      .private-bg { fill: #e8f5e8; stroke: #4caf50; stroke-width: 3; rx: 20; }
      .conclusion-bg { fill: #fff3cd; stroke: #ffc107; stroke-width: 3; rx: 15; }
      .river { fill: #2196f3; opacity: 0.6; }
      .pond { fill: #4caf50; opacity: 0.6; }
      .fish { fill: #ff9800; opacity: 0.8; }
    </style>
    <defs>
      <linearGradient id="river-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" style="stop-color:#2196f3;stop-opacity:0.8" />
        <stop offset="50%" style="stop-color:#03a9f4;stop-opacity:0.6" />
        <stop offset="100%" style="stop-color:#00bcd4;stop-opacity:0.4" />
      </linearGradient>
      <radialGradient id="pond-gradient" cx="50%" cy="50%" r="50%">
        <stop offset="0%" style="stop-color:#4caf50;stop-opacity:0.8" />
        <stop offset="100%" style="stop-color:#8bc34a;stop-opacity:0.6" />
      </radialGradient>
    </defs>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">第二战场：企业微信私域运营</text>
  
  <!-- 副标题 -->
  <text x="960" y="140" text-anchor="middle" class="subtitle">从"一次交易"到"终身关系"的经营</text>
  
  <!-- 左侧：公域流量 -->
  <rect x="100" y="200" width="750" height="600" class="public-bg"/>
  <text x="475" y="250" text-anchor="middle" class="subtitle" fill="#1565c0">公域流量</text>
  
  <!-- 河流图标 -->
  <path d="M 150 300 Q 300 280 450 300 Q 600 320 750 300 Q 850 280 950 300" stroke="url(#river-gradient)" stroke-width="80" fill="none"/>
  <path d="M 150 350 Q 300 330 450 350 Q 600 370 750 350 Q 850 330 950 350" stroke="url(#river-gradient)" stroke-width="60" fill="none"/>
  <path d="M 150 400 Q 300 380 450 400 Q 600 420 750 400 Q 850 380 950 400" stroke="url(#river-gradient)" stroke-width="40" fill="none"/>
  
  <!-- 河流中的鱼（表示流量） -->
  <ellipse cx="300" cy="320" rx="15" ry="8" class="fish"/>
  <ellipse cx="500" cy="340" rx="15" ry="8" class="fish"/>
  <ellipse cx="700" cy="360" rx="15" ry="8" class="fish"/>
  <ellipse cx="400" cy="380" rx="15" ry="8" class="fish"/>
  <ellipse cx="600" cy="400" rx="15" ry="8" class="fish"/>
  
  <!-- 公域特点 -->
  <text x="150" y="500" class="content-text" font-weight="bold">特点：</text>
  <text x="180" y="540" class="feature-text">• 流量如河水，滚滚而来，滚滚而去</text>
  <text x="180" y="570" class="feature-text">• 成本高，需要持续投入</text>
  <text x="180" y="600" class="feature-text">• 用户不属于你，随时可能流失</text>
  <text x="180" y="630" class="feature-text">• 竞争激烈，获客成本不断上升</text>
  <text x="180" y="660" class="feature-text">• 难以建立深度连接</text>
  <text x="180" y="690" class="feature-text">• 用户数据有限，洞察不足</text>
  
  <!-- 右侧：私域流量 -->
  <rect x="1070" y="200" width="750" height="600" class="private-bg"/>
  <text x="1445" y="250" text-anchor="middle" class="subtitle" fill="#2e7d32">私域流量</text>
  
  <!-- 鱼塘图标 -->
  <ellipse cx="1445" cy="380" rx="200" ry="120" fill="url(#pond-gradient)"/>
  <ellipse cx="1445" cy="370" rx="180" ry="100" fill="none" stroke="#4caf50" stroke-width="3"/>
  
  <!-- 鱼塘中的鱼 -->
  <ellipse cx="1380" cy="350" rx="20" ry="12" class="fish"/>
  <ellipse cx="1450" cy="370" rx="25" ry="15" class="fish"/>
  <ellipse cx="1500" cy="390" rx="22" ry="13" class="fish"/>
  <ellipse cx="1420" cy="400" rx="18" ry="10" class="fish"/>
  <ellipse cx="1480" cy="340" rx="20" ry="12" class="fish"/>
  <ellipse cx="1400" cy="380" rx="15" ry="9" class="fish"/>
  
  <!-- 私域特点 -->
  <text x="1120" y="500" class="content-text" font-weight="bold">特点：</text>
  <text x="1150" y="540" class="feature-text">• 流量如池鱼，可以慢慢养，深度培育</text>
  <text x="1150" y="570" class="feature-text">• 成本低，一次获取，长期受益</text>
  <text x="1150" y="600" class="feature-text">• 用户属于你，可控性强</text>
  <text x="1150" y="630" class="feature-text">• 可以建立深度信任关系</text>
  <text x="1150" y="660" class="feature-text">• 精准触达，转化率高</text>
  <text x="1150" y="690" class="feature-text">• 用户数据丰富，洞察深入</text>
  
  <!-- 核心结论 -->
  <rect x="200" y="850" width="1520" height="180" class="conclusion-bg"/>
  <text x="250" y="900" class="subtitle" fill="#f57f17">核心：</text>
  <text x="250" y="940" class="content-text">企微，就是我们精养高价值客户的"私家鱼塘"</text>
  
  <!-- 价值说明 -->
  <rect x="250" y="970" width="1420" height="50" fill="#f1f2f6" stroke="#ddd" stroke-width="1" rx="8"/>
  <text x="270" y="1000" class="content-text" font-weight="bold">从"广场式"的公开营销，转向"会客厅式"的私密服务</text>
  
  <!-- 装饰性元素 -->
  <circle cx="50" cy="500" r="30" fill="#2196f3" opacity="0.3"/>
  <text x="50" y="510" text-anchor="middle" class="content-text" fill="#1565c0" font-size="16px">公域</text>
  
  <circle cx="1870" cy="500" r="30" fill="#4caf50" opacity="0.3"/>
  <text x="1870" y="510" text-anchor="middle" class="content-text" fill="#2e7d32" font-size="16px">私域</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 200 Q 200 180 300 200" stroke="#2196f3" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1720 200 Q 1820 180 1920 200" stroke="#4caf50" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
