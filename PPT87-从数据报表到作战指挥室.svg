<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #2c3e50; line-height: 1.6; }
      .label-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 22px; fill: #7f8c8d; }
      .transform-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #4caf50; font-weight: bold; }
      .main-bg { fill: #f8f9fa; stroke: #e9ecef; stroke-width: 3; rx: 20; }
      .old-bg { fill: #ffebee; stroke: #e74c3c; stroke-width: 2; rx: 15; }
      .new-bg { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; rx: 15; }
      .transform-bg { fill: #fff3cd; stroke: #ffc107; stroke-width: 3; rx: 15; }
      .excel-cell { fill: #ffffff; stroke: #ddd; stroke-width: 1; }
      .dashboard-element { fill: #2196f3; opacity: 0.8; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">从"数据报表"到"作战指挥室"</text>
  
  <!-- 主要内容区域 -->
  <rect x="100" y="150" width="1720" height="750" class="main-bg"/>
  
  <!-- 左图：传统数据报表 -->
  <rect x="200" y="200" width="650" height="400" class="old-bg"/>
  <text x="525" y="240" text-anchor="middle" class="subtitle" fill="#c62828">传统数据报表</text>
  
  <!-- Excel表格模拟 -->
  <rect x="250" y="280" width="550" height="280" fill="#f5f5f5" stroke="#333" stroke-width="2"/>
  
  <!-- 表格网格 -->
  <rect x="250" y="280" width="110" height="40" class="excel-cell"/>
  <rect x="360" y="280" width="110" height="40" class="excel-cell"/>
  <rect x="470" y="280" width="110" height="40" class="excel-cell"/>
  <rect x="580" y="280" width="110" height="40" class="excel-cell"/>
  <rect x="690" y="280" width="110" height="40" class="excel-cell"/>
  
  <rect x="250" y="320" width="110" height="40" class="excel-cell"/>
  <rect x="360" y="320" width="110" height="40" class="excel-cell"/>
  <rect x="470" y="320" width="110" height="40" class="excel-cell"/>
  <rect x="580" y="320" width="110" height="40" class="excel-cell"/>
  <rect x="690" y="320" width="110" height="40" class="excel-cell"/>
  
  <rect x="250" y="360" width="110" height="40" class="excel-cell"/>
  <rect x="360" y="360" width="110" height="40" class="excel-cell"/>
  <rect x="470" y="360" width="110" height="40" class="excel-cell"/>
  <rect x="580" y="360" width="110" height="40" class="excel-cell"/>
  <rect x="690" y="360" width="110" height="40" class="excel-cell"/>
  
  <!-- 密密麻麻的数据模拟 -->
  <text x="305" y="305" text-anchor="middle" class="label-text" font-size="12px">客户ID</text>
  <text x="415" y="305" text-anchor="middle" class="label-text" font-size="12px">ARPU</text>
  <text x="525" y="305" text-anchor="middle" class="label-text" font-size="12px">流量</text>
  <text x="635" y="305" text-anchor="middle" class="label-text" font-size="12px">通话</text>
  <text x="745" y="305" text-anchor="middle" class="label-text" font-size="12px">状态</text>
  
  <text x="305" y="345" text-anchor="middle" class="label-text" font-size="10px">1001</text>
  <text x="415" y="345" text-anchor="middle" class="label-text" font-size="10px">89.5</text>
  <text x="525" y="345" text-anchor="middle" class="label-text" font-size="10px">15.2G</text>
  <text x="635" y="345" text-anchor="middle" class="label-text" font-size="10px">120</text>
  <text x="745" y="345" text-anchor="middle" class="label-text" font-size="10px">正常</text>
  
  <!-- 标签 -->
  <text x="250" y="590" class="label-text" fill="#c62828">信息过载、不直观、延迟</text>
  
  <!-- 右图：现代BI数据看板 -->
  <rect x="1070" y="200" width="650" height="400" class="new-bg"/>
  <text x="1395" y="240" text-anchor="middle" class="subtitle" fill="#2e7d32">现代BI数据看板</text>
  
  <!-- 仪表盘模拟 -->
  <rect x="1120" y="280" width="550" height="280" fill="#f1f2f6" stroke="#333" stroke-width="2" rx="10"/>
  
  <!-- 仪表盘元素 -->
  <circle cx="1250" cy="380" r="40" class="dashboard-element"/>
  <text x="1250" y="385" text-anchor="middle" class="content-text" fill="white" font-size="16px">ARPU</text>
  <text x="1250" y="440" text-anchor="middle" class="label-text" font-size="14px">89.5元</text>
  
  <rect x="1320" y="340" width="80" height="80" class="dashboard-element" rx="10"/>
  <text x="1360" y="375" text-anchor="middle" class="content-text" fill="white" font-size="14px">流失率</text>
  <text x="1360" y="395" text-anchor="middle" class="content-text" fill="white" font-size="16px">2.1%</text>
  <text x="1360" y="440" text-anchor="middle" class="label-text" font-size="14px">↓0.3%</text>
  
  <rect x="1420" y="340" width="80" height="80" class="dashboard-element" rx="10"/>
  <text x="1460" y="375" text-anchor="middle" class="content-text" fill="white" font-size="14px">NPS</text>
  <text x="1460" y="395" text-anchor="middle" class="content-text" fill="white" font-size="16px">72</text>
  <text x="1460" y="440" text-anchor="middle" class="label-text" font-size="14px">↑5</text>
  
  <!-- 趋势图 -->
  <rect x="1520" y="340" width="120" height="80" fill="white" stroke="#ddd" stroke-width="1" rx="5"/>
  <path d="M 1530 400 Q 1560 380 1590 360 Q 1610 350 1630 340" stroke="#4caf50" stroke-width="3" fill="none"/>
  <text x="1580" y="440" text-anchor="middle" class="label-text" font-size="14px">增长趋势</text>
  
  <!-- 标签 -->
  <text x="1120" y="590" class="label-text" fill="#2e7d32">一目了然、实时洞察、辅助决策</text>
  
  <!-- VS标识 -->
  <text x="960" y="420" text-anchor="middle" class="subtitle" font-size="64px" fill="#ff9800">VS</text>
  
  <!-- 核心转变 -->
  <rect x="200" y="650" width="1520" height="200" class="transform-bg"/>
  <text x="250" y="690" class="subtitle" fill="#f57f17">核心转变：</text>
  <text x="250" y="730" class="transform-text">让数据成为"望远镜"和"显微镜"，</text>
  <text x="250" y="770" class="transform-text">而不仅仅是"后视镜"</text>
  <text x="250" y="810" class="content-text">从回顾历史到洞察未来，从信息过载到精准决策</text>
  
  <!-- 装饰性元素 -->
  <circle cx="50" cy="500" r="30" fill="#e74c3c" opacity="0.3"/>
  <text x="50" y="510" text-anchor="middle" class="content-text" fill="#c62828" font-size="16px">报表</text>
  
  <circle cx="1870" cy="500" r="30" fill="#4caf50" opacity="0.3"/>
  <text x="1870" y="510" text-anchor="middle" class="content-text" fill="#2e7d32" font-size="16px">看板</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 150 Q 200 130 300 150" stroke="#e74c3c" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1620 150 Q 1720 130 1820 150" stroke="#4caf50" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
