<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; line-height: 1.6; }
      .funnel-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 22px; fill: #2c3e50; font-weight: bold; }
      .percentage-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; fill: #e74c3c; font-weight: bold; }
      .funnel-bg { fill: #f8f9fa; stroke: #e9ecef; stroke-width: 3; rx: 20; }
      .funnel-level { stroke: #7f8c8d; stroke-width: 2; }
      .leak-point { fill: #e74c3c; stroke: #c62828; stroke-width: 2; rx: 10; }
      .analysis-bg { fill: #fff3cd; stroke: #ffc107; stroke-width: 3; rx: 15; }
    </style>
    <defs>
      <linearGradient id="funnel-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
        <stop offset="0%" style="stop-color:#3498db;stop-opacity:0.8" />
        <stop offset="20%" style="stop-color:#2ecc71;stop-opacity:0.7" />
        <stop offset="40%" style="stop-color:#f39c12;stop-opacity:0.7" />
        <stop offset="60%" style="stop-color:#e67e22;stop-opacity:0.7" />
        <stop offset="80%" style="stop-color:#e74c3c;stop-opacity:0.7" />
        <stop offset="100%" style="stop-color:#c0392b;stop-opacity:0.8" />
      </linearGradient>
    </defs>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">运营第一步：用"漏斗模型"诊断问题</text>
  
  <!-- 漏斗背景区域 -->
  <rect x="100" y="150" width="800" height="700" class="funnel-bg"/>
  
  <!-- 漏斗形状 -->
  <polygon points="200,200 700,200 650,280 250,280" fill="url(#funnel-gradient)" class="funnel-level"/>
  <text x="450" y="230" text-anchor="middle" class="funnel-text" fill="white">曝光 (100%)</text>
  <text x="450" y="255" text-anchor="middle" class="content-text" fill="white" font-size="20px">10000名用户收到提醒短信</text>
  
  <polygon points="250,280 650,280 600,360 300,360" fill="url(#funnel-gradient)" class="funnel-level"/>
  <text x="450" y="310" text-anchor="middle" class="funnel-text" fill="white">点击 (20%)</text>
  <text x="450" y="335" text-anchor="middle" class="content-text" fill="white" font-size="20px">2000名用户点击链接</text>
  
  <polygon points="300,360 600,360 570,440 330,440" fill="url(#funnel-gradient)" class="funnel-level"/>
  <text x="450" y="390" text-anchor="middle" class="funnel-text" fill="white">浏览 (15%)</text>
  <text x="450" y="415" text-anchor="middle" class="content-text" fill="white" font-size="20px">1500名用户浏览页面</text>
  
  <polygon points="330,440 570,440 540,520 360,520" fill="url(#funnel-gradient)" class="funnel-level"/>
  <text x="450" y="470" text-anchor="middle" class="funnel-text" fill="white">选择 (10%)</text>
  <text x="450" y="495" text-anchor="middle" class="content-text" fill="white" font-size="20px">1000名用户选择了产品</text>
  
  <polygon points="360,520 540,520 520,600 380,600" fill="url(#funnel-gradient)" class="funnel-level"/>
  <text x="450" y="550" text-anchor="middle" class="funnel-text" fill="white">确认 (8%)</text>
  <text x="450" y="575" text-anchor="middle" class="content-text" fill="white" font-size="20px">800名用户点击订购</text>
  
  <polygon points="380,600 520,600 510,680 390,680" fill="url(#funnel-gradient)" class="funnel-level"/>
  <text x="450" y="630" text-anchor="middle" class="funnel-text" fill="white">成功 (7.5%)</text>
  <text x="450" y="655" text-anchor="middle" class="content-text" fill="white" font-size="20px">750名用户支付成功</text>
  
  <!-- 漏点标注 -->
  <rect x="750" y="220" width="300" height="80" class="leak-point"/>
  <text x="900" y="250" text-anchor="middle" class="content-text" fill="white" font-weight="bold">最大漏点1</text>
  <text x="900" y="275" text-anchor="middle" class="percentage-text" fill="white">短信点击率（流失80%）</text>
  
  <rect x="750" y="380" width="300" height="80" class="leak-point"/>
  <text x="900" y="410" text-anchor="middle" class="content-text" fill="white" font-weight="bold">最大漏点2</text>
  <text x="900" y="435" text-anchor="middle" class="percentage-text" fill="white">浏览到选择转化率（流失33%）</text>
  
  <!-- 连接线 -->
  <line x1="700" y1="240" x2="750" y2="260" stroke="#e74c3c" stroke-width="3"/>
  <line x1="600" y1="400" x2="750" y2="420" stroke="#e74c3c" stroke-width="3"/>
  
  <!-- 分析区域 -->
  <rect x="1100" y="150" width="720" height="700" class="analysis-bg"/>
  <text x="1150" y="200" class="subtitle" fill="#f57f17">案例分析：流量加油包办理流程</text>
  
  <!-- 问题诊断 -->
  <text x="1150" y="270" class="content-text" font-weight="bold">问题诊断：</text>
  <text x="1170" y="310" class="content-text">• 短信点击率仅20%，说明短信内容缺乏吸引力</text>
  <text x="1170" y="350" class="content-text">• 浏览到选择转化率仅67%，页面设计有问题</text>
  <text x="1170" y="390" class="content-text">• 整体转化率仅7.5%，有巨大优化空间</text>
  
  <!-- 优化建议 -->
  <text x="1150" y="460" class="content-text" font-weight="bold">优化建议：</text>
  <text x="1170" y="500" class="content-text">1. 优化短信文案，增加紧迫感和个性化</text>
  <text x="1170" y="540" class="content-text">2. 简化产品选择页面，突出核心卖点</text>
  <text x="1170" y="580" class="content-text">3. 增加信任背书，降低用户决策门槛</text>
  <text x="1170" y="620" class="content-text">4. 优化支付流程，减少支付环节</text>
  
  <!-- 方法论总结 -->
  <rect x="1150" y="670" width="620" height="150" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="10"/>
  <text x="1170" y="710" class="content-text" font-weight="bold" fill="#2e7d32">漏斗分析方法论：</text>
  <text x="1190" y="740" class="content-text" font-size="20px" fill="#2e7d32">1. 找到最大的"漏点"</text>
  <text x="1190" y="770" class="content-text" font-size="20px" fill="#2e7d32">2. 集中兵力优化关键环节</text>
  <text x="1190" y="800" class="content-text" font-size="20px" fill="#2e7d32">3. 数据驱动，精准改进</text>
  
  <!-- 装饰性元素 -->
  <circle cx="50" cy="400" r="30" fill="#3498db" opacity="0.3"/>
  <text x="50" y="410" text-anchor="middle" class="content-text" fill="#2980b9" font-size="16px">漏斗</text>
  
  <circle cx="1870" cy="400" r="30" fill="#e74c3c" opacity="0.3"/>
  <text x="1870" y="410" text-anchor="middle" class="content-text" fill="#c62828" font-size="16px">优化</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 150 Q 200 130 300 150" stroke="#3498db" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1720 150 Q 1820 130 1920 150" stroke="#e74c3c" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
