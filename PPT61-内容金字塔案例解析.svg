<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #34495e; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 22px; fill: #2c3e50; line-height: 1.6; }
      .example-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; fill: #2c3e50; line-height: 1.5; }
      .value-bg { fill: #e8f5e8; stroke: #4caf50; stroke-width: 3; rx: 15; }
      .interact-bg { fill: #fff3e0; stroke: #ff9800; stroke-width: 3; rx: 15; }
      .marketing-bg { fill: #ffebee; stroke: #e74c3c; stroke-width: 3; rx: 15; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">内容金字塔案例解析</text>
  
  <!-- 价值型内容示例 -->
  <rect x="100" y="150" width="550" height="750" class="value-bg"/>
  <text x="375" y="200" text-anchor="middle" class="section-title" fill="#2e7d32">价值型内容示例</text>
  
  <rect x="130" y="230" width="490" height="120" fill="#f1f8e9" stroke="#8bc34a" stroke-width="2" rx="10"/>
  <text x="150" y="260" class="example-text" font-weight="bold">【通信小科普】</text>
  <text x="150" y="285" class="example-text">手机信号明明满格，为什么网速还是慢？</text>
  <text x="150" y="310" class="example-text">原因可能在这3点...</text>
  
  <rect x="130" y="370" width="490" height="120" fill="#f1f8e9" stroke="#8bc34a" stroke-width="2" rx="10"/>
  <text x="150" y="400" class="example-text" font-weight="bold">【防骗指南】</text>
  <text x="150" y="425" class="example-text">收到这种10086短信，千万别点！</text>
  <text x="150" y="450" class="example-text">最新诈骗手法揭秘！</text>
  
  <rect x="130" y="510" width="490" height="120" fill="#f1f8e9" stroke="#8bc34a" stroke-width="2" rx="10"/>
  <text x="150" y="540" class="example-text" font-weight="bold">【实用技巧】</text>
  <text x="150" y="565" class="example-text">出国旅游，国际漫游这样开通最省钱！</text>
  
  <!-- 互动型内容示例 -->
  <rect x="685" y="150" width="550" height="750" class="interact-bg"/>
  <text x="960" y="200" text-anchor="middle" class="section-title" fill="#e65100">互动型内容示例</text>
  
  <rect x="715" y="230" width="490" height="120" fill="#fff8e1" stroke="#ffb74d" stroke-width="2" rx="10"/>
  <text x="735" y="260" class="example-text">早上好！今天西安有雨，</text>
  <text x="735" y="285" class="example-text">出门记得带伞哦~</text>
  
  <rect x="715" y="370" width="490" height="120" fill="#fff8e1" stroke="#ffb74d" stroke-width="2" rx="10"/>
  <text x="735" y="400" class="example-text" font-weight="bold">【深夜话题】</text>
  <text x="735" y="425" class="example-text">聊聊你用过的第一部手机，</text>
  <text x="735" y="450" class="example-text">是诺基亚还是摩托罗拉？</text>
  
  <rect x="715" y="510" width="490" height="120" fill="#fff8e1" stroke="#ffb74d" stroke-width="2" rx="10"/>
  <text x="735" y="540" class="example-text">（晒一张客户好评截图）</text>
  <text x="735" y="565" class="example-text">感谢王姐的认可，能帮到您就是我最大的开心！</text>
  
  <!-- 营销型内容示例 -->
  <rect x="1270" y="150" width="550" height="750" class="marketing-bg"/>
  <text x="1545" y="200" text-anchor="middle" class="section-title" fill="#c62828">营销型内容示例</text>
  
  <rect x="1300" y="230" width="490" height="140" fill="#fce4ec" stroke="#f48fb1" stroke-width="2" rx="10"/>
  <text x="1320" y="260" class="example-text" font-weight="bold">【社群专属福利】</text>
  <text x="1320" y="285" class="example-text">最后3个名额！今晚12点前，</text>
  <text x="1320" y="310" class="example-text">办理XX宽带套餐，加送一个</text>
  <text x="1320" y="335" class="example-text">价值199元的智能摄像头！</text>
  
  <rect x="1300" y="390" width="490" height="140" fill="#fce4ec" stroke="#f48fb1" stroke-width="2" rx="10"/>
  <text x="1320" y="420" class="example-text" font-weight="bold">【新品首发】</text>
  <text x="1320" y="445" class="example-text">全新5G-A套餐来了！</text>
  <text x="1320" y="470" class="example-text">想第一批体验'身临其境'的通话吗？</text>
  <text x="1320" y="495" class="example-text">私聊我了解详情。</text>
  
  <!-- 装饰性元素 -->
  <circle cx="50" cy="400" r="30" fill="#4caf50" opacity="0.3"/>
  <text x="50" y="410" text-anchor="middle" class="content-text" fill="#2e7d32" font-size="16px">价值</text>
  
  <circle cx="1870" cy="400" r="30" fill="#e74c3c" opacity="0.3"/>
  <text x="1870" y="410" text-anchor="middle" class="content-text" fill="#c62828" font-size="16px">营销</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 150 Q 200 130 300 150" stroke="#4caf50" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1620 150 Q 1720 130 1820 150" stroke="#e74c3c" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
