<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; fill: #34495e; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; fill: #2c3e50; line-height: 1.6; }
      .label { font-family: 'Microsoft YaHei', sans-serif; font-size: 18px; font-weight: bold; fill: #e74c3c; }
      .card-bg { fill: #f8f9fa; stroke: #e9ecef; stroke-width: 2; rx: 15; }
      .avatar-bg { fill: #ecf0f1; stroke: #bdc3c7; stroke-width: 2; rx: 10; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">用户画像示例：程序员张伟</text>
  
  <!-- 主要内容区域 -->
  <rect x="100" y="140" width="1720" height="880" class="card-bg"/>
  
  <!-- 左侧头像区域 -->
  <rect x="150" y="200" width="300" height="400" class="avatar-bg"/>
  <circle cx="300" cy="320" r="80" fill="#3498db" opacity="0.3"/>
  <text x="300" y="330" text-anchor="middle" class="content-text" fill="#7f8c8d">程序员头像</text>
  <text x="300" y="350" text-anchor="middle" class="content-text" fill="#7f8c8d">30多岁男性</text>
  <text x="300" y="370" text-anchor="middle" class="content-text" fill="#7f8c8d">戴眼镜</text>
  <text x="300" y="390" text-anchor="middle" class="content-text" fill="#7f8c8d">格子衫</text>
  
  <!-- 右侧信息区域 -->
  <!-- 基本信息 -->
  <text x="500" y="240" class="section-title">基本信息</text>
  <text x="520" y="280" class="content-text"><tspan class="label">姓名：</tspan>张伟</text>
  <text x="520" y="310" class="content-text"><tspan class="label">年龄：</tspan>35岁</text>
  <text x="520" y="340" class="content-text"><tspan class="label">职业：</tspan>西安市高新区某互联网公司程序员（技术总监）</text>
  <text x="520" y="370" class="content-text"><tspan class="label">家庭状况：</tspan>已婚，有一个上小学的儿子</text>
  <text x="520" y="400" class="content-text"><tspan class="label">使用终端：</tspan>iPhone 14 Pro, iPad Pro</text>
  <text x="520" y="430" class="content-text"><tspan class="label">移动套餐：</tspan>5G畅享188元套餐</text>
  
  <!-- 人物故事 -->
  <text x="500" y="500" class="section-title">人物故事（小传）</text>
  <rect x="520" y="520" width="1200" height="120" fill="#f1f2f6" stroke="#ddd" stroke-width="1" rx="8"/>
  <text x="540" y="550" class="content-text">"张伟是典型的高知、高收入、高压力'三高'人群。工作非常繁忙，生活两点一线。</text>
  <text x="540" y="580" class="content-text">对时间极其敏感，认为'效率就是生命'。是科技爱好者，对各种数码新产品如数家珍，</text>
  <text x="540" y="610" class="content-text">但没时间深入研究通信套餐这类'琐事'。"</text>
  
  <!-- 使用目标 -->
  <text x="500" y="690" class="section-title">使用移动服务的目标 (Goals)</text>
  <text x="520" y="720" class="content-text">1. 网络要绝对稳定、高速，在家、在公司、在路上开视频会议绝不能卡顿。</text>
  <text x="520" y="750" class="content-text">2. 流量要足够多，甚至不用去考虑，有"无限"的感觉最好。</text>
  <text x="520" y="780" class="content-text">3. 希望能有一个包含全家人通信、宽带、娱乐的一站式解决方案，他来付费，省去父母妻儿的麻烦。</text>
  
  <!-- 痛点 -->
  <text x="500" y="840" class="section-title">使用移动服务的痛点 (Frustrations)</text>
  <text x="520" y="870" class="content-text">1. "套餐规则太复杂，各种定向、通用、限速条款，完全没时间看，也看不懂。"</text>
  <text x="520" y="900" class="content-text">2. "偶尔接到营销电话，感觉是在浪费我的宝贵时间。"</text>
  <text x="520" y="930" class="content-text">3. "APP功能繁多，想找一个简单的业务办理入口，像在走迷宫。"</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 140 Q 200 120 300 140" stroke="#3498db" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1620 140 Q 1720 120 1820 140" stroke="#3498db" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
