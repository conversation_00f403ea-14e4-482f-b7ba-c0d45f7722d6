<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #2c3e50; line-height: 1.6; }
      .task-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; line-height: 1.5; }
      .task-bg { fill: #fff3cd; stroke: #ffc107; stroke-width: 3; rx: 20; }
      .scenario-bg { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; rx: 15; }
      .persona-bg { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; rx: 15; }
      .mission-bg { fill: #fce4ec; stroke: #e91e63; stroke-width: 2; rx: 15; }
      .time-bg { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; rx: 15; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">【工作坊任务】</text>
  
  <!-- 主要任务卡区域 -->
  <rect x="200" y="150" width="1520" height="850" class="task-bg"/>
  
  <!-- 剧本设定 -->
  <rect x="250" y="200" width="1420" height="120" class="scenario-bg"/>
  <text x="300" y="240" class="subtitle" fill="#1565c0">你的"剧本"(Scenario)：</text>
  <text x="300" y="280" class="content-text">新用户办理家庭宽带</text>
  <text x="300" y="310" class="task-text">（从产生念头，到安装成功后首次使用）</text>
  
  <!-- 主角设定 -->
  <rect x="250" y="340" width="1420" height="200" class="persona-bg"/>
  <text x="300" y="380" class="subtitle" fill="#2e7d32">你的"主角"(Persona)："焦虑的一家之主"</text>
  
  <text x="350" y="430" class="task-text" font-weight="bold">特征：</text>
  <text x="380" y="460" class="task-text">• 上有老，下有小，工作繁忙</text>
  <text x="380" y="490" class="task-text">• 对通信产品不太懂但要求很高</text>
  <text x="380" y="520" class="task-text">• 最怕麻烦和浪费时间</text>
  
  <!-- 任务要求 -->
  <rect x="250" y="560" width="1420" height="280" class="mission-bg"/>
  <text x="300" y="600" class="subtitle" fill="#c2185b">你的"任务"：</text>
  
  <text x="350" y="650" class="task-text" font-weight="bold">1. 在大白纸上，完整地绘制出这位"一家之主"办理宽带的</text>
  <text x="370" y="680" class="task-text">全过程旅程地图。</text>
  
  <text x="350" y="720" class="task-text" font-weight="bold">2. 重点识别出至少3个让他最不爽的"核心痛点"。</text>
  
  <text x="350" y="760" class="task-text" font-weight="bold">3. 针对每个痛点，提出至少1个可落地的"机会点"</text>
  <text x="370" y="790" class="task-text">（优化建议）。</text>
  
  <!-- 时间要求 -->
  <rect x="250" y="860" width="1420" height="100" class="time-bg"/>
  <text x="300" y="900" class="subtitle" fill="#7b1fa2">时间：</text>
  <text x="450" y="900" class="subtitle" fill="#7b1fa2" font-size="48px">50分钟</text>
  <text x="650" y="900" class="content-text" fill="#7b1fa2">充足时间，深度创作！</text>
  
  <!-- 装饰性元素 -->
  <circle cx="100" cy="500" r="40" fill="#ffc107" opacity="0.3"/>
  <text x="100" y="510" text-anchor="middle" class="content-text" fill="#f57f17" font-size="18px">任务</text>
  
  <circle cx="1820" cy="500" r="40" fill="#e91e63" opacity="0.3"/>
  <text x="1820" y="510" text-anchor="middle" class="content-text" fill="#c2185b" font-size="18px">宽带</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 200 150 Q 300 130 400 150" stroke="#ffc107" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1520 150 Q 1620 130 1720 150" stroke="#e91e63" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
