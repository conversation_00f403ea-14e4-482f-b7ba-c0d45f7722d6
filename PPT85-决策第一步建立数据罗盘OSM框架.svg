<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #2c3e50; line-height: 1.6; }
      .osm-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #2c3e50; line-height: 1.5; }
      .value-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #4caf50; line-height: 1.5; }
      .main-bg { fill: #f8f9fa; stroke: #e9ecef; stroke-width: 3; rx: 20; }
      .osm-bg { fill: #e3f2fd; stroke: #2196f3; stroke-width: 3; rx: 15; }
      .value-bg { fill: #e8f5e8; stroke: #4caf50; stroke-width: 3; rx: 15; }
      .compass { fill: #e74c3c; opacity: 0.8; }
      .target { fill: #ff9800; opacity: 0.8; }
      .path { fill: #2196f3; opacity: 0.8; }
      .gauge { fill: #4caf50; opacity: 0.8; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">决策第一步：建立你的"数据罗盘" (OSM框架)</text>
  
  <!-- 主要内容区域 -->
  <rect x="100" y="150" width="1720" height="750" class="main-bg"/>
  
  <!-- OSM框架展示 -->
  <rect x="200" y="200" width="1520" height="500" class="osm-bg"/>
  
  <!-- O - Objective -->
  <rect x="250" y="250" width="400" height="120" fill="#fff3e0" stroke="#ff9800" stroke-width="2" rx="10"/>
  
  <!-- 靶心图标 -->
  <circle cx="320" cy="310" r="30" class="target"/>
  <circle cx="320" cy="310" r="20" fill="white"/>
  <circle cx="320" cy="310" r="10" class="target"/>
  
  <text x="380" y="290" class="osm-text" font-weight="bold">O (Objective) - 核心目标</text>
  <text x="380" y="320" class="osm-text">我们的靶心在哪？</text>
  
  <!-- S - Strategy -->
  <rect x="760" y="250" width="400" height="120" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="10"/>
  
  <!-- 路径图标 -->
  <rect x="810" y="290" width="60" height="8" class="path" rx="4"/>
  <polygon points="870,294 885,302 870,310" class="path"/>
  <rect x="810" y="310" width="60" height="8" class="path" rx="4"/>
  <polygon points="870,314 885,322 870,330" class="path"/>
  <rect x="810" y="330" width="60" height="8" class="path" rx="4"/>
  <polygon points="870,334 885,342 870,350" class="path"/>
  
  <text x="900" y="290" class="osm-text" font-weight="bold">S (Strategy) - 致胜策略</text>
  <text x="900" y="320" class="osm-text">我们有哪几条路可以到达靶心？</text>
  
  <!-- M - Measurement -->
  <rect x="1270" y="250" width="400" height="120" fill="#fce4ec" stroke="#e91e63" stroke-width="2" rx="10"/>
  
  <!-- 仪表盘图标 -->
  <circle cx="1340" cy="310" r="25" fill="none" stroke="#e91e63" stroke-width="4"/>
  <path d="M 1325 310 L 1340 295 L 1355 310" stroke="#e91e63" stroke-width="3" fill="none"/>
  <circle cx="1340" cy="310" r="3" class="gauge"/>
  
  <text x="1380" y="290" class="osm-text" font-weight="bold">M (Measurement) - 度量指标</text>
  <text x="1380" y="320" class="osm-text">我们如何判断自己走在哪条路上，</text>
  <text x="1380" y="345" class="osm-text">以及离目标还有多远？</text>
  
  <!-- OSM框架说明 -->
  <rect x="250" y="400" width="1420" height="280" fill="#f1f2f6" stroke="#ddd" stroke-width="2" rx="15"/>
  
  <!-- 罗盘图标 -->
  <circle cx="400" cy="540" r="60" class="compass"/>
  <circle cx="400" cy="540" r="45" fill="white"/>
  <text x="400" y="530" text-anchor="middle" class="content-text" fill="#e74c3c" font-size="20px">N</text>
  <text x="400" y="560" text-anchor="middle" class="content-text" fill="#e74c3c" font-size="20px">S</text>
  <text x="380" y="545" text-anchor="middle" class="content-text" fill="#e74c3c" font-size="20px">W</text>
  <text x="420" y="545" text-anchor="middle" class="content-text" fill="#e74c3c" font-size="20px">E</text>
  <line x1="400" y1="520" x2="400" y2="540" stroke="#e74c3c" stroke-width="3"/>
  
  <text x="500" y="520" class="content-text" font-weight="bold">OSM框架的作用：</text>
  <text x="500" y="560" class="content-text">帮助我们从上到下统一思想的目标管理框架</text>
  <text x="500" y="600" class="content-text">避免淹没在浩瀚的数据海洋里，迷失方向</text>
  
  <!-- 核心价值 -->
  <rect x="200" y="730" width="1520" height="120" class="value-bg"/>
  <text x="250" y="770" class="subtitle" fill="#2e7d32">核心价值：</text>
  <text x="250" y="810" class="value-text">确保团队上下同欲，力出一孔</text>
  <text x="250" y="840" class="value-text">让每个人都能在宏大的蓝图中，找到自己工作的价值和意义</text>
  
  <!-- 装饰性元素 -->
  <circle cx="50" cy="500" r="30" fill="#2196f3" opacity="0.3"/>
  <text x="50" y="510" text-anchor="middle" class="content-text" fill="#1565c0" font-size="16px">OSM</text>
  
  <circle cx="1870" cy="500" r="30" fill="#e74c3c" opacity="0.3"/>
  <text x="1870" y="510" text-anchor="middle" class="content-text" fill="#c62828" font-size="16px">罗盘</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 150 Q 200 130 300 150" stroke="#2196f3" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1620 150 Q 1720 130 1820 150" stroke="#e74c3c" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
