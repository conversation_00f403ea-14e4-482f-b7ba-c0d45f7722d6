<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .main-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #2c3e50; }
      .sub-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #2c3e50; line-height: 1.6; }
      .day3-bg { fill: #fff3e0; stroke: #ff9800; stroke-width: 3; rx: 20; }
      .mountain { fill: #8d6e63; opacity: 0.6; }
      .peak { fill: #5d4037; opacity: 0.8; }
    </style>
    <defs>
      <linearGradient id="golden-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#ffc107;stop-opacity:0.8" />
        <stop offset="100%" style="stop-color:#ff9800;stop-opacity:0.4" />
      </linearGradient>
    </defs>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 金色收获背景 -->
  <ellipse cx="960" cy="540" rx="900" ry="450" fill="url(#golden-gradient)"/>
  
  <!-- 山峰轮廓 -->
  <polygon points="100,800 300,600 500,700 700,500 900,650 1100,400 1300,550 1500,350 1700,500 1820,800" class="mountain"/>
  <polygon points="600,500 800,300 1000,400 1200,200 1400,350 1600,800 600,800" class="peak"/>
  
  <!-- 主要内容区域 -->
  <rect x="200" y="150" width="1520" height="600" class="day3-bg"/>
  
  <!-- 主标题 -->
  <text x="960" y="250" text-anchor="middle" class="main-title">Day 3: 价值跃迁</text>
  
  <!-- 副标题 -->
  <text x="960" y="320" text-anchor="middle" class="sub-title">从运营执行到商业增长，</text>
  <text x="960" y="370" text-anchor="middle" class="sub-title">打通价值创造的"最后一公里"</text>
  
  <!-- 本日核心议题 -->
  <text x="960" y="450" text-anchor="middle" class="content-text" font-weight="bold">本日核心议题：</text>
  
  <text x="300" y="520" class="content-text">• 数据驱动与精准营销</text>
  
  <text x="300" y="570" class="content-text">• 主动挽留与价值提升</text>
  
  <text x="300" y="620" class="content-text">• 【终极实战】运营方案设计与汇报</text>
  
  <!-- 登顶旗帜 -->
  <rect x="1400" y="200" width="4" height="100" fill="#e74c3c"/>
  <polygon points="1404,200 1450,220 1404,240" fill="#e74c3c"/>
  
  <!-- 装饰性元素 -->
  <circle cx="100" cy="400" r="40" fill="#ff9800" opacity="0.3"/>
  <text x="100" y="410" text-anchor="middle" class="content-text" fill="#e65100" font-size="18px">Day3</text>
  
  <circle cx="1820" cy="400" r="40" fill="#ffc107" opacity="0.3"/>
  <text x="1820" y="410" text-anchor="middle" class="content-text" fill="#f57f17" font-size="18px">跃迁</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 200 150 Q 300 130 400 150" stroke="#ff9800" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1520 150 Q 1620 130 1720 150" stroke="#ffc107" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
