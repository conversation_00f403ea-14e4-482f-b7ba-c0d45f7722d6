<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #2c3e50; line-height: 1.6; }
      .stage-bg { fill: #1a1a1a; stroke: #333; stroke-width: 3; rx: 20; }
      .spotlight { fill: url(#spotlight-gradient); }
      .requirement-card { fill: #fff3cd; stroke: #ffc107; stroke-width: 3; rx: 15; }
    </style>
    <defs>
      <radialGradient id="spotlight-gradient" cx="50%" cy="30%" r="60%">
        <stop offset="0%" style="stop-color:#ffeb3b;stop-opacity:0.8" />
        <stop offset="70%" style="stop-color:#ffc107;stop-opacity:0.4" />
        <stop offset="100%" style="stop-color:#ff9800;stop-opacity:0.1" />
      </radialGradient>
    </defs>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">成果展示：把我们的"新朋友"介绍给大家</text>
  
  <!-- 舞台背景 -->
  <rect x="200" y="150" width="1520" height="400" class="stage-bg"/>
  
  <!-- 聚光灯效果 -->
  <ellipse cx="960" cy="350" rx="400" ry="200" class="spotlight"/>
  
  <!-- 舞台上的人物剪影 -->
  <circle cx="960" cy="320" r="80" fill="#333" opacity="0.7"/>
  <rect x="920" y="400" width="80" height="120" fill="#333" opacity="0.7" rx="10"/>
  
  <!-- 麦克风 -->
  <rect x="950" y="280" width="20" height="60" fill="#666" rx="10"/>
  <circle cx="960" cy="270" r="15" fill="#888"/>
  
  <!-- 要求区域 -->
  <rect x="150" y="600" width="1620" height="400" class="requirement-card"/>
  
  <text x="200" y="660" class="subtitle">要求：</text>
  
  <text x="250" y="720" class="content-text">• 每个小组推选一名代表。</text>
  
  <text x="250" y="780" class="content-text">• 用3分钟时间，以讲故事的方式，介绍你们的"主角"。</text>
  
  <text x="250" y="840" class="content-text">• 重点阐述他的目标、痛点，以及我们能如何帮助他。</text>
  
  <!-- 装饰性元素 -->
  <circle cx="1650" cy="300" r="50" fill="#ffc107" opacity="0.3"/>
  <text x="1650" y="310" text-anchor="middle" class="content-text" fill="#f57f17" font-size="20px">展示</text>
  
  <circle cx="1650" cy="450" r="50" fill="#e74c3c" opacity="0.3"/>
  <text x="1650" y="460" text-anchor="middle" class="content-text" fill="#c62828" font-size="20px">分享</text>
  
  <!-- 舞台灯光效果 -->
  <rect x="150" y="140" width="50" height="20" fill="#ffc107" rx="5"/>
  <rect x="220" y="140" width="50" height="20" fill="#ffc107" rx="5"/>
  <rect x="290" y="140" width="50" height="20" fill="#ffc107" rx="5"/>
  
  <rect x="1580" y="140" width="50" height="20" fill="#ffc107" rx="5"/>
  <rect x="1650" y="140" width="50" height="20" fill="#ffc107" rx="5"/>
  <rect x="1720" y="140" width="50" height="20" fill="#ffc107" rx="5"/>
  
  <!-- 装饰性弧线 -->
  <path d="M 200 150 Q 300 130 400 150" stroke="#ffc107" stroke-width="4" fill="none" opacity="0.7"/>
  <path d="M 1520 150 Q 1620 130 1720 150" stroke="#ffc107" stroke-width="4" fill="none" opacity="0.7"/>
</svg>
