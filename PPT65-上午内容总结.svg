<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #2c3e50; line-height: 1.6; }
      .weapon-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; line-height: 1.5; }
      .app-bg { fill: #e3f2fd; stroke: #2196f3; stroke-width: 3; rx: 20; }
      .wechat-bg { fill: #e8f5e8; stroke: #4caf50; stroke-width: 3; rx: 20; }
      .conclusion-bg { fill: #fff3cd; stroke: #ffc107; stroke-width: 3; rx: 15; }
      .weapon-icon { fill: #7f8c8d; opacity: 0.6; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">上午内容总结</text>
  
  <!-- 左侧：中国移动APP -->
  <rect x="100" y="150" width="800" height="600" class="app-bg"/>
  
  <!-- APP图标 -->
  <rect x="200" y="200" width="120" height="120" fill="#2196f3" rx="20"/>
  <text x="260" y="270" text-anchor="middle" class="content-text" fill="white" font-size="20px">APP</text>
  
  <text x="350" y="240" class="subtitle" fill="#1565c0">中国移动APP</text>
  <text x="350" y="280" class="weapon-text" fill="#1565c0">核心武器一</text>
  
  <text x="150" y="360" class="weapon-text" font-weight="bold" fill="#1565c0">定位：</text>
  <text x="150" y="390" class="weapon-text" fill="#1565c0">官方商城 和 自助服务大厅</text>
  
  <text x="150" y="450" class="weapon-text" font-weight="bold" fill="#1565c0">核心：</text>
  <text x="150" y="480" class="weapon-text" fill="#1565c0">提升活跃度 (MAU)</text>
  
  <text x="150" y="540" class="weapon-text" font-weight="bold" fill="#1565c0">抓手：</text>
  <text x="150" y="570" class="weapon-text" fill="#1565c0">• 漏斗分析、模块优化</text>
  <text x="150" y="600" class="weapon-text" fill="#1565c0">• 用户成长、精准推送</text>
  
  <!-- 右侧：企业微信 -->
  <rect x="1020" y="150" width="800" height="600" class="wechat-bg"/>
  
  <!-- 企微图标 -->
  <rect x="1120" y="200" width="120" height="120" fill="#4caf50" rx="20"/>
  <text x="1180" y="270" text-anchor="middle" class="content-text" fill="white" font-size="18px">企微</text>
  
  <text x="1270" y="240" class="subtitle" fill="#2e7d32">企业微信</text>
  <text x="1270" y="280" class="weapon-text" fill="#2e7d32">核心武器二</text>
  
  <text x="1070" y="360" class="weapon-text" font-weight="bold" fill="#2e7d32">定位：</text>
  <text x="1070" y="390" class="weapon-text" fill="#2e7d32">私家会客厅 和 关系深耕阵地</text>
  
  <text x="1070" y="450" class="weapon-text" font-weight="bold" fill="#2e7d32">核心：</text>
  <text x="1070" y="480" class="weapon-text" fill="#2e7d32">建立信任 (Relationship)</text>
  
  <text x="1070" y="540" class="weapon-text" font-weight="bold" fill="#2e7d32">抓手：</text>
  <text x="1070" y="570" class="weapon-text" fill="#2e7d32">• 引流策略、IP打造</text>
  <text x="1070" y="600" class="weapon-text" fill="#2e7d32">• 内容矩阵、社群SOP</text>
  
  <!-- VS标识 -->
  <text x="960" y="450" text-anchor="middle" class="subtitle" font-size="64px" fill="#e74c3c">+</text>
  
  <!-- 底部总结 -->
  <rect x="200" y="800" width="1520" height="200" class="conclusion-bg"/>
  <text x="250" y="850" class="subtitle" fill="#f57f17">总结：</text>
  <text x="250" y="890" class="content-text">上午，我们掌握了"武器"；</text>
  <text x="250" y="930" class="content-text">下午，我们将学习"心法"。</text>
  
  <!-- 装饰性元素 -->
  <circle cx="50" cy="450" r="30" fill="#2196f3" opacity="0.3"/>
  <text x="50" y="460" text-anchor="middle" class="content-text" fill="#1565c0" font-size="16px">武器</text>
  
  <circle cx="1870" cy="450" r="30" fill="#4caf50" opacity="0.3"/>
  <text x="1870" y="460" text-anchor="middle" class="content-text" fill="#2e7d32" font-size="16px">心法</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 150 Q 200 130 300 150" stroke="#2196f3" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1620 150 Q 1720 130 1820 150" stroke="#4caf50" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
