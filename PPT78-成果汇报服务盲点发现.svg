<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #2c3e50; line-height: 1.6; }
      .requirement-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #2c3e50; line-height: 1.5; }
      .stage-bg { fill: #fff3e0; stroke: #ff9800; stroke-width: 3; rx: 20; }
      .spotlight { fill: #fff3cd; stroke: #ffc107; stroke-width: 3; rx: 15; }
    </style>
    <defs>
      <radialGradient id="spotlight-gradient" cx="50%" cy="30%" r="70%">
        <stop offset="0%" style="stop-color:#fff3cd;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#ffc107;stop-opacity:0.3" />
      </radialGradient>
    </defs>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 聚光灯效果 -->
  <ellipse cx="960" cy="300" rx="800" ry="200" fill="url(#spotlight-gradient)"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" class="title">成果汇报：那些被我们发现的"服务盲点"</text>
  
  <!-- 主要内容区域 -->
  <rect x="200" y="250" width="1520" height="650" class="stage-bg"/>
  
  <!-- 舞台聚光灯区域 -->
  <rect x="300" y="300" width="1320" height="400" class="spotlight"/>
  
  <!-- 展示台 -->
  <rect x="800" y="500" width="320" height="120" fill="#8d6e63" rx="10"/>
  <rect x="820" y="480" width="280" height="20" fill="#a1887f" rx="5"/>
  
  <!-- 麦克风 -->
  <rect x="950" y="420" width="20" height="60" fill="#333"/>
  <circle cx="960" cy="410" r="15" fill="#666"/>
  <rect x="955" y="480" width="10" height="20" fill="#333"/>
  
  <!-- 要求说明 -->
  <text x="350" y="350" class="subtitle" fill="#e65100">要求：</text>
  
  <text x="380" y="390" class="requirement-text">• 每个小组推选一名代表。</text>
  
  <text x="380" y="430" class="requirement-text">• 用3分钟时间，讲解你们绘制的地图。</text>
  
  <text x="380" y="470" class="requirement-text">• 重点阐述你们发现的"核心痛点"和</text>
  <text x="400" y="500" class="requirement-text">最有创意的"机会点"。</text>
  
  <!-- 鼓励文字 -->
  <text x="960" y="780" text-anchor="middle" class="content-text" font-weight="bold">展示和分享智慧的时刻！</text>
  <text x="960" y="820" text-anchor="middle" class="content-text">有没有哪个小组，愿意作为我们的"先锋"，</text>
  <text x="960" y="860" text-anchor="middle" class="content-text">上台来分享你们的成果？</text>
  
  <!-- 装饰性观众席 -->
  <rect x="250" y="750" width="100" height="60" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" rx="10"/>
  <text x="300" y="785" text-anchor="middle" class="content-text" fill="#1565c0" font-size="20px">观众席</text>
  
  <rect x="1570" y="750" width="100" height="60" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" rx="10"/>
  <text x="1620" y="785" text-anchor="middle" class="content-text" fill="#1565c0" font-size="20px">观众席</text>
  
  <!-- 装饰性元素 -->
  <circle cx="100" cy="500" r="40" fill="#ff9800" opacity="0.3"/>
  <text x="100" y="510" text-anchor="middle" class="content-text" fill="#e65100" font-size="18px">汇报</text>
  
  <circle cx="1820" cy="500" r="40" fill="#ffc107" opacity="0.3"/>
  <text x="1820" y="510" text-anchor="middle" class="content-text" fill="#f57f17" font-size="18px">盲点</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 200 250 Q 300 230 400 250" stroke="#ff9800" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1520 250 Q 1620 230 1720 250" stroke="#ffc107" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
