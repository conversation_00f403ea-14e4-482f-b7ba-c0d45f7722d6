<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #2c3e50; line-height: 1.6; }
      .left-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #7f8c8d; }
      .right-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #27ae60; }
      .arrow { fill: #e74c3c; }
      .left-bg { fill: #ecf0f1; stroke: #bdc3c7; stroke-width: 2; rx: 15; }
      .right-bg { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; rx: 15; }
      .value-bg { fill: #fff3cd; stroke: #ffc107; stroke-width: 3; rx: 15; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">用户画像的魔力</text>
  
  <!-- 左侧区域 -->
  <rect x="150" y="200" width="600" height="400" class="left-bg"/>
  <text x="450" y="260" text-anchor="middle" class="subtitle" fill="#7f8c8d">管理一堆冰冷的数字</text>
  <text x="450" y="320" text-anchor="middle" class="left-text">(Managing Data)</text>
  
  <!-- 数据图标 -->
  <rect x="350" y="360" width="200" height="120" fill="#95a5a6" opacity="0.3" rx="10"/>
  <text x="450" y="400" text-anchor="middle" class="left-text">ARPU: 128元</text>
  <text x="450" y="430" text-anchor="middle" class="left-text">流量: 15GB</text>
  <text x="450" y="460" text-anchor="middle" class="left-text">通话: 300分钟</text>
  
  <!-- 转换箭头 -->
  <polygon points="750,380 850,350 850,370 900,370 900,410 850,410 850,430" class="arrow"/>
  <text x="825" y="480" text-anchor="middle" class="subtitle">思维转变</text>
  
  <!-- 右侧区域 -->
  <rect x="1020" y="200" width="600" height="400" class="right-bg"/>
  <text x="1320" y="260" text-anchor="middle" class="subtitle" fill="#27ae60">服务一个个鲜活的人</text>
  <text x="1320" y="320" text-anchor="middle" class="right-text">(Serving People)</text>
  
  <!-- 人物图标 -->
  <circle cx="1320" cy="420" r="60" fill="#27ae60" opacity="0.3"/>
  <text x="1320" y="430" text-anchor="middle" class="right-text">张伟</text>
  <text x="1320" y="450" text-anchor="middle" class="right-text">程序员</text>
  
  <!-- 核心价值区域 -->
  <rect x="200" y="650" width="1520" height="300" class="value-bg"/>
  <text x="960" y="720" text-anchor="middle" class="subtitle">核心价值：启动同理心，让决策回归用户价值</text>
  
  <text x="250" y="780" class="content-text">✓ 从"完成KPI"到"帮助客户"的思维转变</text>
  <text x="250" y="830" class="content-text">✓ 从"推销产品"到"解决问题"的服务升级</text>
  <text x="250" y="880" class="content-text">✓ 从"管理数字"到"经营关系"的价值重塑</text>
  
  <!-- 装饰性元素 -->
  <circle cx="1750" cy="300" r="40" fill="#e74c3c" opacity="0.3"/>
  <text x="1750" y="310" text-anchor="middle" class="content-text" fill="#c62828" font-size="18px">魔力</text>
  
  <circle cx="1750" cy="500" r="40" fill="#27ae60" opacity="0.3"/>
  <text x="1750" y="510" text-anchor="middle" class="content-text" fill="#1b5e20" font-size="18px">同理心</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 150 200 Q 250 180 350 200" stroke="#e74c3c" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1470 200 Q 1570 180 1670 200" stroke="#27ae60" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
