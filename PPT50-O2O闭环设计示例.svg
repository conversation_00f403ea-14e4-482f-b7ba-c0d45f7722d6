<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 22px; fill: #2c3e50; line-height: 1.6; }
      .step-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; fill: #2c3e50; font-weight: bold; }
      .step-bg { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; rx: 15; }
      .flow-bg { fill: #f8f9fa; stroke: #e9ecef; stroke-width: 3; rx: 20; }
      .conclusion-bg { fill: #e8f5e8; stroke: #4caf50; stroke-width: 3; rx: 15; }
      .arrow { fill: #e74c3c; }
      .number-circle { fill: #2196f3; stroke: #1976d2; stroke-width: 2; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">场景化O2O闭环设计示例</text>
  
  <!-- 流程图背景 -->
  <rect x="100" y="150" width="1720" height="500" class="flow-bg"/>
  
  <!-- 步骤1 -->
  <circle cx="200" cy="250" r="25" class="number-circle"/>
  <text x="200" y="258" text-anchor="middle" class="content-text" fill="white" font-weight="bold">1</text>
  
  <rect x="120" y="290" width="160" height="120" class="step-bg"/>
  <text x="200" y="320" text-anchor="middle" class="step-text">用户在APP上：</text>
  <text x="200" y="345" text-anchor="middle" class="content-text" font-size="18px">看到复杂业务</text>
  <text x="200" y="370" text-anchor="middle" class="content-text" font-size="18px">选择"在线预约"</text>
  
  <!-- 箭头1 -->
  <polygon points="300,340 350,325 350,335 380,335 380,365 350,365 350,375" class="arrow"/>
  
  <!-- 步骤2 -->
  <circle cx="480" cy="250" r="25" class="number-circle"/>
  <text x="480" y="258" text-anchor="middle" class="content-text" fill="white" font-weight="bold">2</text>
  
  <rect x="400" y="290" width="160" height="120" class="step-bg"/>
  <text x="480" y="320" text-anchor="middle" class="step-text">系统后台：</text>
  <text x="480" y="345" text-anchor="middle" class="content-text" font-size="18px">自动派单给</text>
  <text x="480" y="370" text-anchor="middle" class="content-text" font-size="18px">指定营业厅</text>
  
  <!-- 箭头2 -->
  <polygon points="580,340 630,325 630,335 660,335 660,365 630,365 630,375" class="arrow"/>
  
  <!-- 步骤3 -->
  <circle cx="760" cy="250" r="25" class="number-circle"/>
  <text x="760" y="258" text-anchor="middle" class="content-text" fill="white" font-weight="bold">3</text>
  
  <rect x="680" y="290" width="160" height="120" class="step-bg"/>
  <text x="760" y="320" text-anchor="middle" class="step-text">用户到线下厅店：</text>
  <text x="760" y="345" text-anchor="middle" class="content-text" font-size="18px">无需排队</text>
  <text x="760" y="370" text-anchor="middle" class="content-text" font-size="18px">享受VIP式办理</text>
  
  <!-- 箭头3 -->
  <polygon points="860,340 910,325 910,335 940,335 940,365 910,365 910,375" class="arrow"/>
  
  <!-- 步骤4 -->
  <circle cx="1040" cy="250" r="25" class="number-circle"/>
  <text x="1040" y="258" text-anchor="middle" class="content-text" fill="white" font-weight="bold">4</text>
  
  <rect x="960" y="290" width="160" height="120" class="step-bg"/>
  <text x="1040" y="315" text-anchor="middle" class="step-text">柜员在业务办结时：</text>
  <text x="1040" y="340" text-anchor="middle" class="content-text" font-size="18px">引导客户添加</text>
  <text x="1040" y="365" text-anchor="middle" class="content-text" font-size="18px">企业微信</text>
  
  <!-- 箭头4 -->
  <polygon points="1140,340 1190,325 1190,335 1220,335 1220,365 1190,365 1190,375" class="arrow"/>
  
  <!-- 步骤5 -->
  <circle cx="1320" cy="250" r="25" class="number-circle"/>
  <text x="1320" y="258" text-anchor="middle" class="content-text" fill="white" font-weight="bold">5</text>
  
  <rect x="1240" y="290" width="160" height="120" class="step-bg"/>
  <text x="1320" y="320" text-anchor="middle" class="step-text">客户在企微上：</text>
  <text x="1320" y="345" text-anchor="middle" class="content-text" font-size="18px">享受专属</text>
  <text x="1320" y="370" text-anchor="middle" class="content-text" font-size="18px">1v1服务</text>
  
  <!-- 闭环标识 -->
  <rect x="1450" y="290" width="200" height="120" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="10"/>
  <text x="1550" y="330" text-anchor="middle" class="step-text" fill="#f57f17">完美闭环</text>
  <text x="1550" y="355" text-anchor="middle" class="content-text" font-size="18px" fill="#f57f17">线上→线下→私域</text>
  <text x="1550" y="380" text-anchor="middle" class="content-text" font-size="18px" fill="#f57f17">全流程无缝衔接</text>
  
  <!-- 结论区域 -->
  <rect x="150" y="700" width="1620" height="200" class="conclusion-bg"/>
  <text x="200" y="750" class="subtitle" fill="#2e7d32">结论：</text>
  <text x="200" y="790" class="content-text">一次完美的"线上引流→线下体验→私域沉淀"闭环</text>
  
  <!-- 价值分析 -->
  <rect x="200" y="820" width="1520" height="60" fill="#f1f2f6" stroke="#ddd" stroke-width="1" rx="8"/>
  <text x="220" y="845" class="content-text" font-weight="bold">价值体现：</text>
  <text x="220" y="865" class="content-text">客户满意度提升 + 服务效率优化 + 长期关系建立 + 渠道协同增效</text>
  
  <!-- 装饰性元素 -->
  <circle cx="50" cy="350" r="30" fill="#2196f3" opacity="0.3"/>
  <text x="50" y="360" text-anchor="middle" class="content-text" fill="#1565c0" font-size="16px">O2O</text>
  
  <circle cx="1870" cy="350" r="30" fill="#4caf50" opacity="0.3"/>
  <text x="1870" y="360" text-anchor="middle" class="content-text" fill="#2e7d32" font-size="16px">闭环</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 150 Q 200 130 300 150" stroke="#2196f3" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1620 150 Q 1720 130 1820 150" stroke="#4caf50" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
