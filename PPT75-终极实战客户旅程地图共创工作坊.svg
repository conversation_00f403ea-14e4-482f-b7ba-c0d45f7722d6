<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; fill: #2c3e50; line-height: 1.6; }
      .quote-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #4caf50; font-style: italic; }
      .workshop-bg { fill: #fff3e0; stroke: #ff9800; stroke-width: 3; rx: 20; }
      .team-icon { fill: #2196f3; opacity: 0.7; }
    </style>
    <defs>
      <radialGradient id="energy-gradient" cx="50%" cy="50%" r="70%">
        <stop offset="0%" style="stop-color:#ff9800;stop-opacity:0.8" />
        <stop offset="100%" style="stop-color:#e65100;stop-opacity:0.3" />
      </radialGradient>
    </defs>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 能量背景 -->
  <ellipse cx="960" cy="540" rx="900" ry="450" fill="url(#energy-gradient)"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" class="title">【终极实战】客户旅程地图共创工作坊</text>
  
  <!-- 主要内容区域 -->
  <rect x="200" y="250" width="1520" height="600" class="workshop-bg"/>
  
  <!-- 副标题 -->
  <text x="960" y="320" text-anchor="middle" class="subtitle">让我们亲手绘制一张滚烫的、真实的"客户体验地图"</text>
  
  <!-- 团队协作图标 -->
  <circle cx="400" cy="450" r="40" class="team-icon"/>
  <circle cx="500" cy="450" r="40" class="team-icon"/>
  <circle cx="600" cy="450" r="40" class="team-icon"/>
  <circle cx="1320" cy="450" r="40" class="team-icon"/>
  <circle cx="1420" cy="450" r="40" class="team-icon"/>
  <circle cx="1520" cy="450" r="40" class="team-icon"/>
  
  <!-- 连接线表示协作 -->
  <line x1="440" y1="450" x2="460" y2="450" stroke="#2196f3" stroke-width="4"/>
  <line x1="540" y1="450" x2="560" y2="450" stroke="#2196f3" stroke-width="4"/>
  <line x1="1360" y1="450" x2="1380" y2="450" stroke="#2196f3" stroke-width="4"/>
  <line x1="1460" y1="450" x2="1480" y2="450" stroke="#2196f3" stroke-width="4"/>
  
  <!-- 核心理念 -->
  <text x="960" y="550" text-anchor="middle" class="content-text" font-weight="bold">核心：</text>
  <text x="960" y="600" text-anchor="middle" class="quote-text">Empathy is Your Superpower</text>
  <text x="960" y="650" text-anchor="middle" class="quote-text">（共情是你的超能力）</text>
  
  <!-- 激励文字 -->
  <text x="960" y="730" text-anchor="middle" class="content-text">忘掉理论，忘掉PPT，全身心地投入到创作和共情中去</text>
  <text x="960" y="780" text-anchor="middle" class="content-text">这是最激动人心、也最考验团队智慧的时刻！</text>
  
  <!-- 装饰性地图元素 -->
  <rect x="700" y="380" width="120" height="80" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" rx="10" opacity="0.7"/>
  <text x="760" y="425" text-anchor="middle" class="content-text" fill="#1565c0" font-size="20px">地图</text>
  
  <rect x="1100" y="380" width="120" height="80" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="10" opacity="0.7"/>
  <text x="1160" y="425" text-anchor="middle" class="content-text" fill="#2e7d32" font-size="20px">共创</text>
  
  <!-- 装饰性元素 -->
  <circle cx="100" cy="500" r="40" fill="#ff9800" opacity="0.3"/>
  <text x="100" y="510" text-anchor="middle" class="content-text" fill="#e65100" font-size="18px">实战</text>
  
  <circle cx="1820" cy="500" r="40" fill="#e74c3c" opacity="0.3"/>
  <text x="1820" y="510" text-anchor="middle" class="content-text" fill="#c62828" font-size="18px">共情</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 200 250 Q 300 230 400 250" stroke="#ff9800" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1520 250 Q 1620 230 1720 250" stroke="#e74c3c" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
