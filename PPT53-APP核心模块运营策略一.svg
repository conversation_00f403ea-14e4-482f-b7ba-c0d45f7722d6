<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; line-height: 1.6; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #34495e; }
      .question-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 22px; fill: #e74c3c; font-style: italic; }
      .phone-bg { fill: #f8f9fa; stroke: #333; stroke-width: 3; rx: 25; }
      .screen-bg { fill: #ffffff; stroke: #ddd; stroke-width: 1; rx: 15; }
      .golden-section { fill: #fff3cd; stroke: #ffc107; stroke-width: 2; rx: 10; }
      .growth-section { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; rx: 10; }
      .icon-bg { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; rx: 8; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">【现场巡店】APP核心模块运营策略（一）</text>
  
  <!-- 左侧：首页金刚位 -->
  <rect x="100" y="150" width="850" height="800" class="golden-section"/>
  <text x="150" y="200" class="section-title">首页"黄金地段"运营</text>
  
  <!-- 手机界面 -->
  <rect x="150" y="250" width="300" height="500" class="phone-bg"/>
  <rect x="170" y="280" width="260" height="440" class="screen-bg"/>
  
  <!-- APP顶部 -->
  <rect x="190" y="300" width="220" height="40" fill="#2196f3" rx="5"/>
  <text x="300" y="325" text-anchor="middle" class="content-text" fill="white" font-size="18px">中国移动</text>
  
  <!-- 金刚位区域 -->
  <rect x="190" y="360" width="220" height="120" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="8"/>
  <text x="300" y="385" text-anchor="middle" class="content-text" font-size="16px" font-weight="bold">金刚位区域</text>
  
  <!-- 金刚位图标 -->
  <rect x="200" y="400" width="40" height="40" class="icon-bg"/>
  <text x="220" y="425" text-anchor="middle" class="content-text" font-size="12px">话费</text>
  
  <rect x="250" y="400" width="40" height="40" class="icon-bg"/>
  <text x="270" y="425" text-anchor="middle" class="content-text" font-size="12px">流量</text>
  
  <rect x="300" y="400" width="40" height="40" class="icon-bg"/>
  <text x="320" y="425" text-anchor="middle" class="content-text" font-size="12px">套餐</text>
  
  <rect x="350" y="400" width="40" height="40" class="icon-bg"/>
  <text x="370" y="425" text-anchor="middle" class="content-text" font-size="12px">缴费</text>
  
  <rect x="200" y="450" width="40" height="40" class="icon-bg"/>
  <text x="220" y="475" text-anchor="middle" class="content-text" font-size="12px">客服</text>
  
  <rect x="250" y="450" width="40" height="40" class="icon-bg"/>
  <text x="270" y="475" text-anchor="middle" class="content-text" font-size="12px">积分</text>
  
  <rect x="300" y="450" width="40" height="40" class="icon-bg"/>
  <text x="320" y="475" text-anchor="middle" class="content-text" font-size="12px">活动</text>
  
  <rect x="350" y="450" width="40" height="40" class="icon-bg"/>
  <text x="370" y="475" text-anchor="middle" class="content-text" font-size="12px">更多</text>
  
  <!-- 金刚位说明 -->
  <rect x="500" y="250" width="400" height="500" fill="#f1f2f6" stroke="#ddd" stroke-width="2" rx="10"/>
  <text x="520" y="290" class="content-text" font-weight="bold">金刚位：</text>
  <text x="520" y="320" class="content-text">用户最高频、最刚需的功能入口</text>
  
  <text x="520" y="380" class="question-text">互动思考：</text>
  <text x="520" y="410" class="content-text" font-size="20px">我们现在的金刚位布局，合理吗？</text>
  
  <text x="520" y="460" class="content-text" font-weight="bold">布局原则：</text>
  <text x="540" y="490" class="content-text" font-size="20px">• 高频使用</text>
  <text x="540" y="520" class="content-text" font-size="20px">• 刚性需求</text>
  <text x="540" y="550" class="content-text" font-size="20px">• 用户习惯</text>
  <text x="540" y="580" class="content-text" font-size="20px">• 商业价值</text>
  
  <text x="520" y="630" class="content-text" font-weight="bold">优化建议：</text>
  <text x="540" y="660" class="content-text" font-size="20px">• 基于用户行为数据调整</text>
  <text x="540" y="690" class="content-text" font-size="20px">• 定期A/B测试验证效果</text>
  <text x="540" y="720" class="content-text" font-size="20px">• 考虑个性化推荐</text>
  
  <!-- 右侧：用户成长体系 -->
  <rect x="1000" y="150" width="820" height="800" class="growth-section"/>
  <text x="1050" y="200" class="section-title">用户成长体系搭建</text>
  
  <!-- 成长体系界面 -->
  <rect x="1050" y="250" width="300" height="500" class="phone-bg"/>
  <rect x="1070" y="280" width="260" height="440" class="screen-bg"/>
  
  <!-- 签到模块 -->
  <rect x="1090" y="300" width="220" height="80" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="8"/>
  <text x="1200" y="330" text-anchor="middle" class="content-text" font-size="16px" font-weight="bold">每日签到</text>
  <text x="1200" y="355" text-anchor="middle" class="content-text" font-size="14px">连续签到7天，获得流量包</text>
  
  <!-- 任务模块 -->
  <rect x="1090" y="390" width="220" height="100" fill="#fff3e0" stroke="#ff9800" stroke-width="2" rx="8"/>
  <text x="1200" y="420" text-anchor="middle" class="content-text" font-size="16px" font-weight="bold">每日任务</text>
  <text x="1200" y="445" text-anchor="middle" class="content-text" font-size="14px">• 查看话费余额 +10积分</text>
  <text x="1200" y="465" text-anchor="middle" class="content-text" font-size="14px">• 分享给朋友 +20积分</text>
  
  <!-- 积分商城 -->
  <rect x="1090" y="500" width="220" height="80" fill="#fce4ec" stroke="#e91e63" stroke-width="2" rx="8"/>
  <text x="1200" y="530" text-anchor="middle" class="content-text" font-size="16px" font-weight="bold">积分商城</text>
  <text x="1200" y="555" text-anchor="middle" class="content-text" font-size="14px">积分兑换话费、流量、礼品</text>
  
  <!-- 成长体系说明 -->
  <rect x="1400" y="250" width="370" height="500" fill="#f1f2f6" stroke="#ddd" stroke-width="2" rx="10"/>
  <text x="1420" y="290" class="content-text" font-weight="bold">任务/签到/积分：</text>
  <text x="1420" y="320" class="content-text">提升用户粘性的"游戏化"机制</text>
  
  <text x="1420" y="380" class="question-text">互动思考：</text>
  <text x="1420" y="410" class="content-text" font-size="20px">现在的任务和奖励，对你有吸引力吗？</text>
  <text x="1420" y="440" class="content-text" font-size="20px">如何让它更好玩？</text>
  
  <text x="1420" y="490" class="content-text" font-weight="bold">设计要点：</text>
  <text x="1440" y="520" class="content-text" font-size="20px">• 奖励要有吸引力</text>
  <text x="1440" y="550" class="content-text" font-size="20px">• 任务要简单易完成</text>
  <text x="1440" y="580" class="content-text" font-size="20px">• 要有成就感和仪式感</text>
  <text x="1440" y="610" class="content-text" font-size="20px">• 建立长期激励机制</text>
  
  <text x="1420" y="660" class="content-text" font-weight="bold">HOOK模型：</text>
  <text x="1440" y="690" class="content-text" font-size="20px">触发→行动→奖励→投入</text>
  <text x="1440" y="720" class="content-text" font-size="20px">让用户形成使用习惯</text>
  
  <!-- 装饰性元素 -->
  <circle cx="50" cy="400" r="30" fill="#ffc107" opacity="0.3"/>
  <text x="50" y="410" text-anchor="middle" class="content-text" fill="#f57f17" font-size="16px">金刚</text>
  
  <circle cx="1870" cy="400" r="30" fill="#4caf50" opacity="0.3"/>
  <text x="1870" y="410" text-anchor="middle" class="content-text" fill="#2e7d32" font-size="16px">成长</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 150 Q 200 130 300 150" stroke="#ffc107" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1720 150 Q 1820 130 1920 150" stroke="#4caf50" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
