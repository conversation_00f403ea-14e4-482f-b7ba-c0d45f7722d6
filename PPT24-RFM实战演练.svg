<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <text x="960" y="100" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="#003d7a">【实战演练】RFM客户分层练习</text>
  
  <!-- 任务说明框 -->
  <rect x="200" y="180" width="1520" height="300" rx="20" fill="#f0f8ff" stroke="#0066cc" stroke-width="3"/>
  <text x="960" y="230" text-anchor="middle" font-family="Microsoft YaHei" font-size="42" font-weight="bold" fill="#0066cc">任务说明</text>
  
  <!-- 任务步骤 -->
  <circle cx="300" cy="290" r="25" fill="#cc3333"/>
  <text x="300" y="300" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">1</text>
  <text x="350" y="280" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#003d7a">打分：</text>
  <text x="350" y="310" font-family="Microsoft YaHei" font-size="24" fill="#333">根据学员手册P9页的5个虚拟客户案例和评分标准，为R/F/M打分。</text>
  
  <circle cx="300" cy="360" r="25" fill="#009900"/>
  <text x="300" y="370" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">2</text>
  <text x="350" y="350" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#003d7a">归类：</text>
  <text x="350" y="380" font-family="Microsoft YaHei" font-size="24" fill="#333">参照小组桌上的"RFM客户分层与应对策略矩阵"海报，判断客户类型。</text>
  
  <circle cx="300" cy="430" r="25" fill="#ff9900"/>
  <text x="300" y="440" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">3</text>
  <text x="350" y="420" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#003d7a">贴策：</text>
  <text x="350" y="450" font-family="Microsoft YaHei" font-size="24" fill="#333">用便利贴写出核心应对策略，并贴在海报相应位置。</text>
  
  <!-- 时间框 -->
  <rect x="400" y="520" width="1120" height="120" rx="20" fill="#ff6b35" stroke="#ff6b35" stroke-width="3"/>
  <text x="960" y="570" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="white">时间：15分钟</text>
  <text x="960" y="610" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" fill="white">这是一个非常重要的练习，请大家务必亲手操作！</text>
  
  <!-- RFM矩阵示例 -->
  <rect x="200" y="680" width="1520" height="300" rx="20" fill="#f8f8f8" stroke="#666" stroke-width="2"/>
  <text x="960" y="720" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#003d7a">RFM客户分层矩阵示例</text>
  
  <!-- 客户类型示例 -->
  <rect x="250" y="750" width="200" height="80" rx="10" fill="#ffd700" stroke="#ff6600" stroke-width="2"/>
  <text x="350" y="780" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#cc3333">重要价值客户</text>
  <text x="350" y="805" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#333">高R+高F+高M</text>
  
  <rect x="480" y="750" width="200" height="80" rx="10" fill="#87ceeb" stroke="#0066cc" stroke-width="2"/>
  <text x="580" y="780" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#0066cc">重要发展客户</text>
  <text x="580" y="805" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#333">高R+低F+高M</text>
  
  <rect x="710" y="750" width="200" height="80" rx="10" fill="#98fb98" stroke="#009900" stroke-width="2"/>
  <text x="810" y="780" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#009900">重要保持客户</text>
  <text x="810" y="805" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#333">低R+高F+高M</text>
  
  <rect x="940" y="750" width="200" height="80" rx="10" fill="#ffa500" stroke="#ff9900" stroke-width="2"/>
  <text x="1040" y="780" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#ff9900">重要挽留客户</text>
  <text x="1040" y="805" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#333">低R+低F+高M</text>
  
  <rect x="1170" y="750" width="200" height="80" rx="10" fill="#f0f0f0" stroke="#666" stroke-width="2"/>
  <text x="1270" y="780" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#666">一般客户</text>
  <text x="1270" y="805" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#333">中R+中F+中M</text>
  
  <!-- 装饰元素 -->
  <g fill="#0066cc" opacity="0.3">
    <!-- 表格图标 -->
    <rect x="1600" y="300" width="80" height="60" rx="5" fill="none" stroke="#0066cc" stroke-width="3"/>
    <line x1="1620" y1="300" x2="1620" y2="360" stroke="#0066cc" stroke-width="2"/>
    <line x1="1640" y1="300" x2="1640" y2="360" stroke="#0066cc" stroke-width="2"/>
    <line x1="1660" y1="300" x2="1660" y2="360" stroke="#0066cc" stroke-width="2"/>
    <line x1="1600" y1="320" x2="1680" y2="320" stroke="#0066cc" stroke-width="2"/>
    <line x1="1600" y1="340" x2="1680" y2="340" stroke="#0066cc" stroke-width="2"/>
  </g>
</svg>
