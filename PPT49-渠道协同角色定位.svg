<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .header-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 22px; font-weight: bold; fill: #2c3e50; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; fill: #2c3e50; }
      .table-header { fill: #3498db; stroke: #2980b9; stroke-width: 2; }
      .table-row1 { fill: #ecf0f1; stroke: #bdc3c7; stroke-width: 1; }
      .table-row2 { fill: #f8f9fa; stroke: #bdc3c7; stroke-width: 1; }
      .matrix-bg { fill: #e8f5e8; stroke: #4caf50; stroke-width: 3; rx: 15; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">渠道协同第一步：角色再定位</text>
  
  <!-- 矩阵表格背景 -->
  <rect x="100" y="150" width="1720" height="750" class="matrix-bg"/>
  
  <!-- 表格标题行 -->
  <rect x="150" y="200" width="300" height="60" class="table-header"/>
  <text x="300" y="240" text-anchor="middle" class="header-text" fill="white">渠道</text>
  
  <rect x="450" y="200" width="400" height="60" class="table-header"/>
  <text x="650" y="240" text-anchor="middle" class="header-text" fill="white">核心角色定位</text>
  
  <rect x="850" y="200" width="400" height="60" class="table-header"/>
  <text x="1050" y="240" text-anchor="middle" class="header-text" fill="white">协同辅助角色</text>
  
  <rect x="1250" y="200" width="520" height="60" class="table-header"/>
  <text x="1510" y="240" text-anchor="middle" class="header-text" fill="white">关键协同动作</text>
  
  <!-- 线下营业厅行 -->
  <rect x="150" y="260" width="300" height="100" class="table-row1"/>
  <text x="300" y="300" text-anchor="middle" class="content-text" font-weight="bold">线下营业厅</text>
  <text x="300" y="325" text-anchor="middle" class="content-text" font-size="16px">实体服务中心</text>
  
  <rect x="450" y="260" width="400" height="100" class="table-row1"/>
  <text x="650" y="290" text-anchor="middle" class="content-text">品牌体验中心</text>
  <text x="650" y="315" text-anchor="middle" class="content-text">复杂业务终结者</text>
  <text x="650" y="340" text-anchor="middle" class="content-text">高价值客户接待</text>
  
  <rect x="850" y="260" width="400" height="100" class="table-row1"/>
  <text x="1050" y="290" text-anchor="middle" class="content-text">线上渠道引流器</text>
  <text x="1050" y="315" text-anchor="middle" class="content-text">APP推广站点</text>
  <text x="1050" y="340" text-anchor="middle" class="content-text">企微添加入口</text>
  
  <rect x="1250" y="260" width="520" height="100" class="table-row1"/>
  <text x="1510" y="285" text-anchor="middle" class="content-text" font-size="18px">引导客户下载APP</text>
  <text x="1510" y="310" text-anchor="middle" class="content-text" font-size="18px">推荐添加企业微信</text>
  <text x="1510" y="335" text-anchor="middle" class="content-text" font-size="18px">提供预约服务</text>
  
  <!-- 中国移动APP行 -->
  <rect x="150" y="360" width="300" height="100" class="table-row2"/>
  <text x="300" y="400" text-anchor="middle" class="content-text" font-weight="bold">中国移动APP</text>
  <text x="300" y="425" text-anchor="middle" class="content-text" font-size="16px">线上服务平台</text>
  
  <rect x="450" y="360" width="400" height="100" class="table-row2"/>
  <text x="650" y="390" text-anchor="middle" class="content-text">自助服务大厅</text>
  <text x="650" y="415" text-anchor="middle" class="content-text">标准化产品交易平台</text>
  
  <rect x="850" y="360" width="400" height="100" class="table-row2"/>
  <text x="1050" y="390" text-anchor="middle" class="content-text">线下导流平台</text>
  <text x="1050" y="415" text-anchor="middle" class="content-text">私域流量输送器</text>
  
  <rect x="1250" y="360" width="520" height="100" class="table-row2"/>
  <text x="1510" y="385" text-anchor="middle" class="content-text" font-size="18px">提供在线预约功能</text>
  <text x="1510" y="410" text-anchor="middle" class="content-text" font-size="18px">引导添加企业微信</text>
  <text x="1510" y="435" text-anchor="middle" class="content-text" font-size="18px">推送个性化服务</text>
  
  <!-- 企业微信行 -->
  <rect x="150" y="460" width="300" height="100" class="table-row1"/>
  <text x="300" y="500" text-anchor="middle" class="content-text" font-weight="bold">企业微信</text>
  <text x="300" y="525" text-anchor="middle" class="content-text" font-size="16px">私域运营工具</text>
  
  <rect x="450" y="460" width="400" height="100" class="table-row1"/>
  <text x="650" y="490" text-anchor="middle" class="content-text">私域流量池</text>
  <text x="650" y="515" text-anchor="middle" class="content-text">1v1专属服务平台</text>
  
  <rect x="850" y="460" width="400" height="100" class="table-row1"/>
  <text x="1050" y="490" text-anchor="middle" class="content-text">线下服务延伸</text>
  <text x="1050" y="515" text-anchor="middle" class="content-text">APP功能补充</text>
  
  <rect x="1250" y="460" width="520" height="100" class="table-row1"/>
  <text x="1510" y="485" text-anchor="middle" class="content-text" font-size="18px">提供专属客服</text>
  <text x="1510" y="510" text-anchor="middle" class="content-text" font-size="18px">推送个性化内容</text>
  <text x="1510" y="535" text-anchor="middle" class="content-text" font-size="18px">维护客户关系</text>
  
  <!-- 10086热线行 -->
  <rect x="150" y="560" width="300" height="100" class="table-row2"/>
  <text x="300" y="600" text-anchor="middle" class="content-text" font-weight="bold">10086热线</text>
  <text x="300" y="625" text-anchor="middle" class="content-text" font-size="16px">电话服务中心</text>
  
  <rect x="450" y="560" width="400" height="100" class="table-row2"/>
  <text x="650" y="590" text-anchor="middle" class="content-text">应急服务热线</text>
  <text x="650" y="615" text-anchor="middle" class="content-text">问题解决中心</text>
  
  <rect x="850" y="560" width="400" height="100" class="table-row2"/>
  <text x="1050" y="590" text-anchor="middle" class="content-text">其他渠道引流</text>
  <text x="1050" y="615" text-anchor="middle" class="content-text">服务体验收集</text>
  
  <rect x="1250" y="560" width="520" height="100" class="table-row2"/>
  <text x="1510" y="585" text-anchor="middle" class="content-text" font-size="18px">引导使用APP</text>
  <text x="1510" y="610" text-anchor="middle" class="content-text" font-size="18px">推荐企微服务</text>
  <text x="1510" y="635" text-anchor="middle" class="content-text" font-size="18px">收集客户反馈</text>
  
  <!-- 装维工程师行 -->
  <rect x="150" y="660" width="300" height="100" class="table-row1"/>
  <text x="300" y="700" text-anchor="middle" class="content-text" font-weight="bold">装维工程师</text>
  <text x="300" y="725" text-anchor="middle" class="content-text" font-size="16px">上门服务专员</text>
  
  <rect x="450" y="660" width="400" height="100" class="table-row1"/>
  <text x="650" y="690" text-anchor="middle" class="content-text">技术服务专家</text>
  <text x="650" y="715" text-anchor="middle" class="content-text">客户关系维护者</text>
  
  <rect x="850" y="660" width="400" height="100" class="table-row1"/>
  <text x="1050" y="690" text-anchor="middle" class="content-text">私域流量收割</text>
  <text x="1050" y="715" text-anchor="middle" class="content-text">服务满意度提升</text>
  
  <rect x="1250" y="660" width="520" height="100" class="table-row1"/>
  <text x="1510" y="685" text-anchor="middle" class="content-text" font-size="18px">服务后添加企微</text>
  <text x="1510" y="710" text-anchor="middle" class="content-text" font-size="18px">推广APP使用</text>
  <text x="1510" y="735" text-anchor="middle" class="content-text" font-size="18px">收集服务反馈</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 150 Q 200 130 300 150" stroke="#4caf50" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1620 150 Q 1720 130 1820 150" stroke="#4caf50" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
