<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #2c3e50; line-height: 1.6; }
      .theory-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; line-height: 1.5; }
      .strategy-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 22px; fill: #2c3e50; line-height: 1.5; }
      .main-bg { fill: #f8f9fa; stroke: #e9ecef; stroke-width: 3; rx: 20; }
      .theory-bg { fill: #e3f2fd; stroke: #2196f3; stroke-width: 3; rx: 15; }
      .strategy-bg { fill: #e8f5e8; stroke: #4caf50; stroke-width: 3; rx: 15; }
      .curve { stroke: #ff9800; stroke-width: 4; fill: none; }
      .peak-point { fill: #e74c3c; }
      .end-point { fill: #4caf50; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">体验设计的秘诀：峰终定律 (Peak-End Rule)</text>
  
  <!-- 主要内容区域 -->
  <rect x="100" y="150" width="1720" height="750" class="main-bg"/>
  
  <!-- 情绪曲线图示 -->
  <rect x="200" y="200" width="800" height="300" fill="#f1f2f6" stroke="#ddd" stroke-width="2" rx="15"/>
  
  <!-- 坐标轴 -->
  <line x1="250" y1="450" x2="950" y2="450" stroke="#7f8c8d" stroke-width="2"/>
  <line x1="250" y1="250" x2="250" y2="450" stroke="#7f8c8d" stroke-width="2"/>
  
  <!-- 情绪曲线 -->
  <path d="M 250 400 Q 350 380 450 350 Q 550 320 650 280 Q 750 300 850 250 Q 900 270 950 260" class="curve"/>
  
  <!-- 峰值标注 -->
  <circle cx="650" cy="280" r="8" class="peak-point"/>
  <text x="650" y="260" text-anchor="middle" class="theory-text" font-weight="bold" fill="#e74c3c">Peak</text>
  <text x="650" y="240" text-anchor="middle" class="theory-text" fill="#e74c3c">体验最高峰</text>
  
  <!-- 终值标注 -->
  <circle cx="950" cy="260" r="8" class="end-point"/>
  <text x="950" y="240" text-anchor="middle" class="theory-text" font-weight="bold" fill="#4caf50">End</text>
  <text x="950" y="220" text-anchor="middle" class="theory-text" fill="#4caf50">体验结束时</text>
  
  <!-- 坐标轴标签 -->
  <text x="200" y="350" text-anchor="middle" class="theory-text" fill="#7f8c8d">情绪</text>
  <text x="600" y="480" text-anchor="middle" class="theory-text" fill="#7f8c8d">时间</text>
  
  <!-- 核心理论 -->
  <rect x="1050" y="200" width="670" height="300" class="theory-bg"/>
  <text x="1100" y="240" class="subtitle" fill="#1565c0">核心理论：</text>
  
  <text x="1100" y="280" class="theory-text">一段体验留给人的记忆，</text>
  <text x="1100" y="310" class="theory-text">几乎完全由两个时刻决定：</text>
  
  <text x="1120" y="350" class="theory-text" font-weight="bold">1. 体验最高峰</text>
  <text x="1140" y="380" class="theory-text">（最好或最坏的时刻）</text>
  
  <text x="1120" y="420" class="theory-text" font-weight="bold">2. 体验结束时的感觉</text>
  
  <!-- 我们的策略 -->
  <rect x="200" y="550" width="1520" height="300" class="strategy-bg"/>
  <text x="250" y="590" class="subtitle" fill="#2e7d32">我们的策略：</text>
  
  <text x="300" y="640" class="strategy-text" font-weight="bold">• 避免糟糕的"波谷"（负向峰值）</text>
  <text x="320" y="670" class="strategy-text">识别并优化让客户最愤怒的环节，消除负面记忆点</text>
  
  <text x="300" y="720" class="strategy-text" font-weight="bold">• 精心设计积极的"波峰"（正向峰值）</text>
  <text x="320" y="750" class="strategy-text">在某个环节创造超预期的惊喜，建立正面记忆点</text>
  
  <text x="300" y="800" class="strategy-text" font-weight="bold">• 确保一个美好的"终值"（结尾）</text>
  <text x="320" y="830" class="strategy-text">让业务办完的最后一个环节是愉快的，留下好印象</text>
  
  <!-- 装饰性元素 -->
  <circle cx="50" cy="500" r="30" fill="#ff9800" opacity="0.3"/>
  <text x="50" y="510" text-anchor="middle" class="content-text" fill="#e65100" font-size="16px">峰值</text>
  
  <circle cx="1870" cy="500" r="30" fill="#4caf50" opacity="0.3"/>
  <text x="1870" y="510" text-anchor="middle" class="content-text" fill="#2e7d32" font-size="16px">终值</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 150 Q 200 130 300 150" stroke="#ff9800" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1620 150 Q 1720 130 1820 150" stroke="#4caf50" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
