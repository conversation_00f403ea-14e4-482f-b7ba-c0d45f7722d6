<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 咖啡杯图标 -->
  <g transform="translate(960, 400)">
    <!-- 杯子主体 -->
    <ellipse cx="0" cy="100" rx="120" ry="20" fill="#8B4513"/>
    <rect x="-120" y="0" width="240" height="100" fill="#D2691E"/>
    <ellipse cx="0" cy="0" rx="120" ry="20" fill="#F4A460"/>
    
    <!-- 咖啡液面 -->
    <ellipse cx="0" cy="10" rx="100" ry="15" fill="#654321"/>
    
    <!-- 杯把手 -->
    <path d="M 120 30 Q 160 30 160 60 Q 160 90 120 90" fill="none" stroke="#8B4513" stroke-width="12"/>
    
    <!-- 蒸汽 -->
    <g stroke="#cccccc" stroke-width="3" fill="none" opacity="0.7">
      <path d="M -40 -20 Q -35 -40 -40 -60 Q -45 -80 -40 -100">
        <animate attributeName="opacity" values="0.7;0.3;0.7" dur="2s" repeatCount="indefinite"/>
      </path>
      <path d="M 0 -20 Q 5 -40 0 -60 Q -5 -80 0 -100">
        <animate attributeName="opacity" values="0.3;0.7;0.3" dur="2.5s" repeatCount="indefinite"/>
      </path>
      <path d="M 40 -20 Q 45 -40 40 -60 Q 35 -80 40 -100">
        <animate attributeName="opacity" values="0.7;0.3;0.7" dur="3s" repeatCount="indefinite"/>
      </path>
    </g>
  </g>
  
  <!-- 主文字 -->
  <text x="960" y="650" text-anchor="middle" font-family="Microsoft YaHei" font-size="72" font-weight="bold" fill="#8B4513">短暂休息，精彩继续</text>
  <text x="960" y="720" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" fill="#D2691E">(Coffee Break)</text>
  
  <!-- 时间 -->
  <rect x="700" y="780" width="520" height="120" rx="20" fill="#F4A460" stroke="#8B4513" stroke-width="3"/>
  <text x="960" y="830" text-anchor="middle" font-family="Microsoft YaHei" font-size="42" font-weight="bold" fill="#8B4513">时间</text>
  <text x="960" y="880" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#654321">10:30 - 10:45</text>
  
  <!-- 装饰咖啡豆 -->
  <g fill="#654321" opacity="0.3">
    <ellipse cx="300" cy="200" rx="15" ry="20" transform="rotate(30 300 200)"/>
    <ellipse cx="1620" cy="200" rx="15" ry="20" transform="rotate(-30 1620 200)"/>
    <ellipse cx="200" cy="800" rx="15" ry="20" transform="rotate(45 200 800)"/>
    <ellipse cx="1720" cy="800" rx="15" ry="20" transform="rotate(-45 1720 800)"/>
    <ellipse cx="400" cy="900" rx="15" ry="20" transform="rotate(60 400 900)"/>
    <ellipse cx="1520" cy="900" rx="15" ry="20" transform="rotate(-60 1520 900)"/>
  </g>
  
  <!-- 装饰弧线 -->
  <path d="M 200 950 Q 960 920 1720 950" stroke="#D2691E" stroke-width="3" fill="none" opacity="0.5"/>
  <path d="M 300 150 Q 960 120 1620 150" stroke="#D2691E" stroke-width="3" fill="none" opacity="0.5"/>
</svg>
