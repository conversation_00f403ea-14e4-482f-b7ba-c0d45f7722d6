<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <text x="960" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="#003d7a">认知革命的起点：两个黄金公式</text>
  
  <!-- 石碑背景装饰 -->
  <rect x="200" y="200" width="1520" height="700" rx="30" fill="#f8f8f8" stroke="#666" stroke-width="4"/>
  <rect x="220" y="220" width="1480" height="660" rx="20" fill="#fafafa" stroke="#999" stroke-width="2"/>
  
  <!-- 公式一 -->
  <rect x="300" y="300" width="1320" height="180" rx="20" fill="#e6f3ff" stroke="#0066cc" stroke-width="3"/>
  <text x="960" y="360" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#003d7a">公式一</text>
  <text x="960" y="430" text-anchor="middle" font-family="Microsoft YaHei" font-size="72" font-weight="bold" fill="#0066cc">LTV &gt; CAC</text>
  <text x="960" y="470" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" fill="#666">(客户生命周期价值 &gt; 客户获取成本)</text>
  
  <!-- 公式二 -->
  <rect x="300" y="520" width="1320" height="180" rx="20" fill="#e6f3ff" stroke="#0066cc" stroke-width="3"/>
  <text x="960" y="580" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#003d7a">公式二</text>
  <text x="960" y="650" text-anchor="middle" font-family="Microsoft YaHei" font-size="72" font-weight="bold" fill="#0066cc">NPS ≈ Growth</text>
  <text x="960" y="690" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" fill="#666">(净推荐值 ≈ 增长)</text>
  
  <!-- 装饰元素 -->
  <g fill="#d4af37" opacity="0.6">
    <!-- 左侧装饰 -->
    <polygon points="150,400 170,380 190,400 170,420" fill="#d4af37"/>
    <polygon points="150,600 170,580 190,600 170,620" fill="#d4af37"/>
    
    <!-- 右侧装饰 -->
    <polygon points="1730,400 1750,380 1770,400 1750,420" fill="#d4af37"/>
    <polygon points="1730,600 1750,580 1770,600 1750,620" fill="#d4af37"/>
  </g>
  
  <!-- 底部装饰弧线 -->
  <path d="M 200 750 Q 960 720 1720 750" stroke="#0066cc" stroke-width="3" fill="none" opacity="0.5"/>
  <path d="M 300 800 Q 960 830 1620 800" stroke="#0066cc" stroke-width="2" fill="none" opacity="0.3"/>
  
  <!-- 顶部装饰 -->
  <path d="M 200 250 Q 960 220 1720 250" stroke="#0066cc" stroke-width="2" fill="none" opacity="0.4"/>
</svg>
