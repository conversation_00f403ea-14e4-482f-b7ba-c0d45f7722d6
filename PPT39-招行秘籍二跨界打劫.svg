<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; line-height: 1.6; }
      .flow-bg { fill: #f8f9fa; stroke: #e9ecef; stroke-width: 2; rx: 15; }
      .step-bg { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; rx: 10; }
      .hook-bg { fill: #fff3e0; stroke: #ff9800; stroke-width: 2; rx: 10; }
      .result-bg { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; rx: 10; }
      .conclusion-bg { fill: #fce4ec; stroke: #e91e63; stroke-width: 3; rx: 15; }
      .arrow { fill: #e74c3c; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">招行秘籍二：高频打低频的"跨界打劫"</text>
  
  <!-- 核心打法 -->
  <text x="960" y="150" text-anchor="middle" class="subtitle">核心打法：用生活场景，撬动金融场景</text>
  
  <!-- 流程图背景 -->
  <rect x="100" y="200" width="1720" height="300" class="flow-bg"/>
  
  <!-- 流程步骤 -->
  <!-- 起点 -->
  <rect x="150" y="250" width="300" height="100" class="step-bg"/>
  <text x="300" y="290" text-anchor="middle" class="content-text" font-weight="bold">起点：生活场景</text>
  <text x="300" y="320" text-anchor="middle" class="content-text">吃饭、看电影</text>
  
  <!-- 箭头1 -->
  <polygon points="470,290 520,270 520,285 570,285 570,315 520,315 520,330" class="arrow"/>
  
  <!-- 钩子 -->
  <rect x="590" y="250" width="300" height="100" class="hook-bg"/>
  <text x="740" y="280" text-anchor="middle" class="content-text" font-weight="bold">钩子："饭票"、"影票"</text>
  <text x="740" y="310" text-anchor="middle" class="content-text">5折等强力优惠</text>
  
  <!-- 箭头2 -->
  <polygon points="910,290 960,270 960,285 1010,285 1010,315 960,315 960,330" class="arrow"/>
  
  <!-- 动作 -->
  <rect x="1030" y="250" width="300" height="100" class="step-bg"/>
  <text x="1180" y="280" text-anchor="middle" class="content-text" font-weight="bold">动作：用户为省钱</text>
  <text x="1180" y="310" text-anchor="middle" class="content-text">打开APP购买饭票</text>
  
  <!-- 箭头3 -->
  <polygon points="1350,290 1400,270 1400,285 1450,285 1450,315 1400,315 1400,330" class="arrow"/>
  
  <!-- 结果 -->
  <rect x="1470" y="230" width="300" height="140" class="result-bg"/>
  <text x="1620" y="270" text-anchor="middle" class="content-text" font-weight="bold">结果：</text>
  <text x="1620" y="300" text-anchor="middle" class="content-text" font-size="20px">成功引导至APP</text>
  <text x="1620" y="325" text-anchor="middle" class="content-text" font-size="20px">增加打开频率</text>
  <text x="1620" y="350" text-anchor="middle" class="content-text" font-size="20px">"顺便"浏览金融产品</text>
  
  <!-- 详细解释区域 -->
  <rect x="150" y="550" width="1620" height="200" class="flow-bg"/>
  <text x="200" y="600" class="subtitle">威力分析：</text>
  <text x="250" y="650" class="content-text">• 用户为了省10块钱饭钱，打开招行APP买饭票</text>
  <text x="250" y="690" class="content-text">• 完成购买后，会"顺便"查看工资到账情况</text>
  <text x="250" y="730" class="content-text">• 可能"顺便"浏览首页推荐的理财产品（7日年化收益率4%）</text>
  
  <!-- 结论区域 -->
  <rect x="150" y="800" width="1620" height="200" class="conclusion-bg"/>
  <text x="200" y="850" class="subtitle">结论：</text>
  <text x="200" y="890" class="content-text">找到用户的"高频刚需"，用它作为钩子，带动你想推广的"低频高价值"业务。</text>
  <text x="200" y="930" class="content-text">这是一种顶级高明的运营智慧：不带任何骚扰感地，巧妙引导用户使用核心功能。</text>
  
  <!-- 装饰性元素 -->
  <circle cx="50" cy="300" r="30" fill="#ff9800" opacity="0.3"/>
  <text x="50" y="310" text-anchor="middle" class="content-text" fill="#e65100" font-size="16px">高频</text>
  
  <circle cx="1870" cy="300" r="30" fill="#4caf50" opacity="0.3"/>
  <text x="1870" y="310" text-anchor="middle" class="content-text" fill="#2e7d32" font-size="16px">低频</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 200 Q 200 180 300 200" stroke="#e74c3c" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1620 200 Q 1720 180 1820 200" stroke="#e74c3c" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
