<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 22px; fill: #2c3e50; line-height: 1.6; }
      .push-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; fill: #2c3e50; line-height: 1.5; }
      .good-bg { fill: #e8f5e8; stroke: #4caf50; stroke-width: 3; rx: 15; }
      .bad-bg { fill: #ffebee; stroke: #e74c3c; stroke-width: 3; rx: 15; }
      .conclusion-bg { fill: #fff3cd; stroke: #ffc107; stroke-width: 3; rx: 15; }
      .feature-bg { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; rx: 10; }
      .vs-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #e74c3c; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">【现场巡店】APP核心模块运营策略（二）</text>
  
  <!-- 左侧：好的Push -->
  <rect x="100" y="150" width="750" height="650" class="good-bg"/>
  <text x="150" y="200" class="subtitle" fill="#2e7d32">好的Push 👍</text>
  
  <!-- 好的Push案例 -->
  <rect x="150" y="240" width="650" height="200" fill="#f1f8e9" stroke="#8bc34a" stroke-width="2" rx="10"/>
  <text x="170" y="270" class="content-text" font-weight="bold">案例：</text>
  <text x="170" y="300" class="push-text">"Hi，小主~ 您的88元套餐本月流量已用掉90%啦！</text>
  <text x="170" y="325" class="push-text">别慌，我们为您准备了专属的10元10G流量加油包，</text>
  <text x="170" y="350" class="push-text">够您刷完这部剧了哟~ [点击急救]"</text>
  
  <!-- 好的Push特点 -->
  <text x="150" y="490" class="content-text" font-weight="bold">特点：</text>
  <rect x="150" y="510" width="650" height="280" class="feature-bg"/>
  <text x="180" y="550" class="content-text">✓ 场景化：基于用户真实使用场景</text>
  <text x="180" y="580" class="content-text">✓ 个性化：针对具体用户的具体情况</text>
  <text x="180" y="610" class="content-text">✓ 语气亲切：像朋友一样关心用户</text>
  <text x="180" y="640" class="content-text">✓ 行动点明确：告诉用户具体怎么做</text>
  <text x="180" y="670" class="content-text">✓ 解决问题：真正帮助用户解决困扰</text>
  <text x="180" y="700" class="content-text">✓ 时机精准：在用户最需要的时候出现</text>
  <text x="180" y="730" class="content-text">✓ 价值明确：清楚说明能带来什么好处</text>
  
  <!-- VS标识 -->
  <text x="960" y="450" text-anchor="middle" class="vs-text">VS</text>
  
  <!-- 右侧：坏的Push -->
  <rect x="1070" y="150" width="750" height="650" class="bad-bg"/>
  <text x="1120" y="200" class="subtitle" fill="#c62828">坏的Push 👎</text>
  
  <!-- 坏的Push案例 -->
  <rect x="1120" y="240" width="650" height="200" fill="#fce4ec" stroke="#f48fb1" stroke-width="2" rx="10"/>
  <text x="1140" y="270" class="content-text" font-weight="bold">案例：</text>
  <text x="1140" y="300" class="push-text">"【中国移动】5G套餐，火热办理中！</text>
  <text x="1140" y="325" class="push-text">高速网络，快人一步！</text>
  <text x="1140" y="350" class="push-text">点击办理！"</text>
  
  <!-- 坏的Push特点 -->
  <text x="1120" y="490" class="content-text" font-weight="bold">特点：</text>
  <rect x="1120" y="510" width="650" height="280" class="feature-bg"/>
  <text x="1150" y="550" class="content-text">✗ 内容空洞：没有实质性价值信息</text>
  <text x="1150" y="580" class="content-text">✗ 无差别轰炸：所有人收到相同内容</text>
  <text x="1150" y="610" class="content-text">✗ 骚扰感强：纯粹的广告推销</text>
  <text x="1150" y="640" class="content-text">✗ 时机不当：不考虑用户状态和需求</text>
  <text x="1150" y="670" class="content-text">✗ 语言生硬：官方腔调，缺乏温度</text>
  <text x="1150" y="700" class="content-text">✗ 缺乏针对性：没有考虑用户画像</text>
  <text x="1150" y="730" class="content-text">✗ 容易被忽略：用户会直接删除或屏蔽</text>
  
  <!-- 核心结论 -->
  <rect x="200" y="850" width="1520" height="180" class="conclusion-bg"/>
  <text x="250" y="900" class="subtitle" fill="#f57f17">核心结论：</text>
  <text x="250" y="940" class="content-text">做"贴心管家"，而非"推销走卒"</text>
  
  <!-- 具体建议 -->
  <rect x="250" y="970" width="1420" height="50" fill="#f1f2f6" stroke="#ddd" stroke-width="1" rx="8"/>
  <text x="270" y="1000" class="content-text" font-weight="bold">Push黄金法则：在对的时间，对对的人，说对的话，解决对的问题</text>
  
  <!-- 装饰性元素 -->
  <circle cx="50" cy="400" r="30" fill="#4caf50" opacity="0.3"/>
  <text x="50" y="410" text-anchor="middle" class="content-text" fill="#2e7d32" font-size="16px">好</text>
  
  <circle cx="1870" cy="400" r="30" fill="#e74c3c" opacity="0.3"/>
  <text x="1870" y="410" text-anchor="middle" class="content-text" fill="#c62828" font-size="16px">坏</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 150 Q 200 130 300 150" stroke="#4caf50" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1720 150 Q 1820 130 1920 150" stroke="#e74c3c" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
