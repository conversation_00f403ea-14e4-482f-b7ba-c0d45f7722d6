<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; line-height: 1.6; }
      .task-card { fill: #fff3cd; stroke: #ffc107; stroke-width: 3; rx: 15; }
      .option-card { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; rx: 10; }
      .requirement-card { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; rx: 10; }
      .time-card { fill: #e8f5e8; stroke: #4caf50; stroke-width: 3; rx: 15; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">【工作坊】用户画像共创</text>
  
  <!-- 任务卡片 -->
  <rect x="150" y="150" width="1620" height="800" class="task-card"/>
  
  <!-- 任务标题 -->
  <text x="200" y="220" class="subtitle">任务：请以小组为单位，从以下客群中四选一，作为你们的创作"主角"</text>
  
  <!-- 选项区域 -->
  <rect x="200" y="260" width="1520" height="280" class="option-card"/>
  <text x="250" y="310" class="content-text">1. 西安市的Z世代在校大学生</text>
  <text x="250" y="350" class="content-text">2. 宝鸡市的"小镇中年"体制内员工</text>
  <text x="250" y="390" class="content-text">3. 榆林市的能源行业商务人士</text>
  <text x="250" y="430" class="content-text">4. 安康市的留守老人与儿童家庭</text>
  
  <!-- 要求区域 -->
  <text x="200" y="600" class="subtitle">要求：</text>
  <rect x="200" y="620" width="1520" height="200" class="requirement-card"/>
  <text x="250" y="670" class="content-text">• 在A3画布上，完成一份完整、生动、细节丰富的用户画像。</text>
  <text x="250" y="710" class="content-text">• 充分利用你们的真实观察、经验，甚至是合理的想象。</text>
  <text x="250" y="750" class="content-text">• 记住，细节越丰富，画像越"活"。</text>
  
  <!-- 时间区域 -->
  <rect x="600" y="860" width="720" height="80" class="time-card"/>
  <text x="960" y="910" text-anchor="middle" class="subtitle" fill="#2e7d32">时间：35分钟</text>
  
  <!-- 装饰性元素 -->
  <circle cx="1700" cy="300" r="60" fill="#ffc107" opacity="0.3"/>
  <text x="1700" y="310" text-anchor="middle" class="content-text" fill="#f57f17">创意</text>
  
  <circle cx="1700" cy="500" r="60" fill="#2196f3" opacity="0.3"/>
  <text x="1700" y="510" text-anchor="middle" class="content-text" fill="#1565c0">协作</text>
  
  <circle cx="1700" cy="700" r="60" fill="#9c27b0" opacity="0.3"/>
  <text x="1700" y="710" text-anchor="middle" class="content-text" fill="#6a1b9a">画像</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 150 150 Q 250 130 350 150" stroke="#ffc107" stroke-width="4" fill="none" opacity="0.7"/>
  <path d="M 1570 150 Q 1670 130 1770 150" stroke="#ffc107" stroke-width="4" fill="none" opacity="0.7"/>
</svg>
