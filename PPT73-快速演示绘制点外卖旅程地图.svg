<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #2c3e50; line-height: 1.6; }
      .demo-bg { fill: #f8f9fa; stroke: #e9ecef; stroke-width: 3; rx: 20; }
      .whiteboard-bg { fill: #ffffff; stroke: #333; stroke-width: 4; rx: 10; }
      .marker { fill: #e74c3c; opacity: 0.8; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">【快速演示】绘制一张"点外卖"的旅程地图</text>
  
  <!-- 主要内容区域 -->
  <rect x="100" y="150" width="1720" height="750" class="demo-bg"/>
  
  <!-- 白板区域 -->
  <rect x="200" y="200" width="1520" height="600" class="whiteboard-bg"/>
  
  <!-- 白板标题 -->
  <text x="960" y="250" text-anchor="middle" class="subtitle">现场演示白板</text>
  
  <!-- 演示提示 -->
  <rect x="300" y="300" width="1320" height="400" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="15"/>
  
  <text x="350" y="350" class="content-text" font-weight="bold" fill="#f57f17">演示场景：用APP点一份外卖</text>
  
  <text x="350" y="420" class="content-text" fill="#f57f17">主角：我们自己</text>
  <text x="350" y="460" class="content-text" fill="#f57f17">剧本：中午肚子饿了，想点一份麻辣烫</text>
  
  <text x="350" y="530" class="content-text" fill="#f57f17">阶段划分：</text>
  <text x="380" y="570" class="content-text" fill="#f57f17">选择商家 → 下单支付 → 等待配送 → 收到餐品 → 用餐评价</text>
  
  <text x="350" y="640" class="content-text" fill="#f57f17">重点分析："等待配送"阶段的痛点与机会点</text>
  
  <!-- 马克笔图标 -->
  <rect x="250" y="350" width="20" height="100" class="marker" rx="10"/>
  <rect x="245" y="340" width="30" height="20" fill="#333" rx="5"/>
  
  <rect x="1650" y="450" width="20" height="100" class="marker" rx="10"/>
  <rect x="1645" y="440" width="30" height="20" fill="#333" rx="5"/>
  
  <!-- 讲师备注区域 -->
  <rect x="200" y="830" width="1520" height="120" fill="#e8f5e8" stroke="#4caf50" stroke-width="3" rx="15"/>
  <text x="250" y="870" class="content-text" font-weight="bold" fill="#2e7d32">讲师现场演示要点：</text>
  <text x="250" y="910" class="content-text" fill="#2e7d32">此页PPT仅为标题，讲师需要走到白板前，进行一次快速、生动、互动的现场演示</text>
  <text x="250" y="940" class="content-text" fill="#2e7d32">通过"点外卖"这个日常场景，让学员直观理解旅程地图的绘制方法</text>
  
  <!-- 装饰性元素 -->
  <circle cx="50" cy="500" r="30" fill="#e74c3c" opacity="0.3"/>
  <text x="50" y="510" text-anchor="middle" class="content-text" fill="#c62828" font-size="16px">演示</text>
  
  <circle cx="1870" cy="500" r="30" fill="#4caf50" opacity="0.3"/>
  <text x="1870" y="510" text-anchor="middle" class="content-text" fill="#2e7d32" font-size="16px">互动</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 150 Q 200 130 300 150" stroke="#e74c3c" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1620 150 Q 1720 130 1820 150" stroke="#4caf50" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
