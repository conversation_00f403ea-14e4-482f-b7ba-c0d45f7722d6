<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .day-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; line-height: 1.6; }
      .solved-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 22px; fill: #27ae60; }
      .will-solve-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 22px; fill: #e74c3c; }
      .left-bg { fill: #e3f2fd; stroke: #2196f3; stroke-width: 3; rx: 20; }
      .right-bg { fill: #ffebee; stroke: #e74c3c; stroke-width: 3; rx: 20; }
      .arrow { fill: #34495e; }
      .brain-icon { fill: #3498db; opacity: 0.6; }
      .hand-icon { fill: #e74c3c; opacity: 0.6; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">从"战略"到"战术"的思维切换</text>
  
  <!-- 大脑图标 -->
  <circle cx="300" cy="200" r="60" class="brain-icon"/>
  <text x="300" y="210" text-anchor="middle" class="content-text" fill="white" font-size="20px">大脑</text>
  
  <!-- 转换箭头 -->
  <polygon points="500,190 600,170 600,185 700,185 700,215 600,215 600,230" class="arrow"/>
  <text x="600" y="260" text-anchor="middle" class="content-text" font-weight="bold">思维切换</text>
  
  <!-- 手部操作图标 -->
  <rect x="850" y="160" width="80" height="80" class="hand-icon" rx="10"/>
  <text x="890" y="210" text-anchor="middle" class="content-text" fill="white" font-size="18px">实操</text>
  
  <!-- 左侧：Day 1 战略重塑 -->
  <rect x="100" y="320" width="700" height="600" class="left-bg"/>
  <text x="450" y="370" text-anchor="middle" class="day-title" fill="#1565c0">Day 1 · 战略重塑</text>
  
  <text x="150" y="430" class="content-text" font-weight="bold">我们解决了：</text>
  
  <text x="180" y="480" class="solved-text" font-weight="bold">Why (为什么要做？)</text>
  <text x="200" y="510" class="solved-text">→ 应对挑战，抓住机遇</text>
  
  <text x="180" y="560" class="solved-text" font-weight="bold">Who (该对谁做？)</text>
  <text x="200" y="590" class="solved-text">→ RFM价值分层</text>
  <text x="200" y="620" class="solved-text">→ Persona用户画像</text>
  
  <!-- 战略成果展示 -->
  <rect x="150" y="670" width="600" height="200" fill="#f8f9fa" stroke="#ddd" stroke-width="1" rx="10"/>
  <text x="170" y="710" class="content-text" font-weight="bold">战略成果：</text>
  <text x="190" y="740" class="content-text">✓ 统一思想：客户是资产</text>
  <text x="190" y="770" class="content-text">✓ 明确方向：AARRR模型</text>
  <text x="190" y="800" class="content-text">✓ 掌握方法：RFM + Persona</text>
  <text x="190" y="830" class="content-text">✓ 建立共识：价值共生理念</text>
  
  <!-- 右侧：Day 2 战术攻坚 -->
  <rect x="1120" y="320" width="700" height="600" class="right-bg"/>
  <text x="1470" y="370" text-anchor="middle" class="day-title">Day 2 · 战术攻坚</text>
  
  <text x="1170" y="430" class="content-text" font-weight="bold">我们将解决：</text>
  
  <text x="1200" y="480" class="will-solve-text" font-weight="bold">Where (在哪里做？)</text>
  <text x="1220" y="510" class="will-solve-text">→ 核心渠道：APP与企业微信</text>
  
  <text x="1200" y="560" class="will-solve-text" font-weight="bold">How (具体怎么做？)</text>
  <text x="1220" y="590" class="will-solve-text">→ 精细化运营策略</text>
  <text x="1220" y="620" class="will-solve-text">→ 服务体验优化</text>
  
  <!-- 战术目标展示 -->
  <rect x="1170" y="670" width="600" height="200" fill="#f8f9fa" stroke="#ddd" stroke-width="1" rx="10"/>
  <text x="1190" y="710" class="content-text" font-weight="bold">战术目标：</text>
  <text x="1210" y="740" class="content-text">• 掌握APP运营心法</text>
  <text x="1210" y="770" class="content-text">• 玩转企微私域流量</text>
  <text x="1210" y="800" class="content-text">• 绘制客户旅程地图</text>
  <text x="1210" y="830" class="content-text">• 优化服务体验</text>
  
  <!-- 装饰性元素 -->
  <circle cx="50" cy="500" r="30" fill="#2196f3" opacity="0.3"/>
  <text x="50" y="510" text-anchor="middle" class="content-text" fill="#1565c0" font-size="16px">战略</text>
  
  <circle cx="1870" cy="500" r="30" fill="#e74c3c" opacity="0.3"/>
  <text x="1870" y="510" text-anchor="middle" class="content-text" fill="#c62828" font-size="16px">战术</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 320 Q 200 300 300 320" stroke="#2196f3" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1620 320 Q 1720 300 1820 320" stroke="#e74c3c" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
