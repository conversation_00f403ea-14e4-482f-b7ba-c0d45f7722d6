<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; line-height: 1.6; }
      .speech-bubble { fill: #fff3cd; stroke: #ffc107; stroke-width: 2; rx: 15; }
      .scene-bg { fill: #f8f9fa; stroke: #e9ecef; stroke-width: 2; rx: 15; }
      .question-bg { fill: #e8f5e8; stroke: #4caf50; stroke-width: 3; rx: 15; }
      .person { fill: #3498db; opacity: 0.7; }
      .staff { fill: #e74c3c; opacity: 0.7; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">一个普遍的痛点：我们的渠道在"打架"吗？</text>
  
  <!-- 场景一 -->
  <rect x="100" y="150" width="800" height="350" class="scene-bg"/>
  <text x="150" y="190" class="subtitle" fill="#e74c3c">场景一：营业厅困惑</text>
  
  <!-- 客户图标 -->
  <circle cx="250" cy="280" r="40" class="person"/>
  <text x="250" y="290" text-anchor="middle" class="content-text" fill="white" font-size="16px">客户</text>
  
  <!-- 客户对话气泡 -->
  <rect x="320" y="220" width="350" height="80" class="speech-bubble"/>
  <text x="340" y="250" class="content-text">"我在你们APP上看到这个活动，</text>
  <text x="340" y="280" class="content-text">为什么你们这办不了？"</text>
  
  <!-- 柜员图标 -->
  <circle cx="250" cy="400" r="40" class="staff"/>
  <text x="250" y="410" text-anchor="middle" class="content-text" fill="white" font-size="16px">柜员</text>
  
  <!-- 柜员对话气泡 -->
  <rect x="320" y="360" width="300" height="60" class="speech-bubble"/>
  <text x="340" y="385" class="content-text">"我们没接到通知啊……"</text>
  <text x="340" y="410" class="content-text">（一脸茫然）</text>
  
  <!-- 场景二 -->
  <rect x="1020" y="150" width="800" height="350" class="scene-bg"/>
  <text x="1070" y="190" class="subtitle" fill="#e74c3c">场景二：客户经理无奈</text>
  
  <!-- 客户经理图标 -->
  <circle cx="1170" cy="280" r="40" class="staff"/>
  <text x="1170" y="290" text-anchor="middle" class="content-text" fill="white" font-size="14px">客户经理</text>
  
  <!-- 客户经理对话气泡 -->
  <rect x="1240" y="220" width="350" height="80" class="speech-bubble"/>
  <text x="1260" y="250" class="content-text">"王哥，我辛辛苦苦发展的客户……"</text>
  <text x="1260" y="280" class="content-text">（被线上优惠活动抢走了）</text>
  
  <!-- 手机图标表示线上活动 -->
  <rect x="1200" y="350" width="80" height="120" fill="#2196f3" opacity="0.3" rx="10"/>
  <text x="1240" y="420" text-anchor="middle" class="content-text" fill="#1565c0" font-size="16px">线上优惠</text>
  
  <!-- 中心提问区域 -->
  <rect x="200" y="550" width="1520" height="200" class="question-bg"/>
  <text x="250" y="600" class="subtitle" fill="#2e7d32">中心提问：</text>
  <text x="250" y="650" class="content-text">如何让我们的渠道，从"互相抢客"的博弈关系，</text>
  <text x="250" y="690" class="content-text">走向"协同作战"的共生关系？</text>
  
  <!-- 问题分析 -->
  <rect x="200" y="780" width="1520" height="220" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="10"/>
  <text x="250" y="830" class="subtitle" fill="#f57f17">问题根源：</text>
  <text x="300" y="870" class="content-text">• 信息不同步：线上线下活动信息不一致</text>
  <text x="300" y="910" class="content-text">• 利益冲突：不同渠道各自为战，缺乏协同机制</text>
  <text x="300" y="950" class="content-text">• 客户体验割裂：同一客户在不同渠道获得不同体验</text>
  
  <!-- 装饰性元素 -->
  <circle cx="50" cy="300" r="30" fill="#e74c3c" opacity="0.3"/>
  <text x="50" y="310" text-anchor="middle" class="content-text" fill="#c62828" font-size="16px">冲突</text>
  
  <circle cx="1870" cy="300" r="30" fill="#4caf50" opacity="0.3"/>
  <text x="1870" y="310" text-anchor="middle" class="content-text" fill="#2e7d32" font-size="16px">协同</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 150 Q 200 130 300 150" stroke="#e74c3c" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1620 150 Q 1720 130 1820 150" stroke="#e74c3c" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
