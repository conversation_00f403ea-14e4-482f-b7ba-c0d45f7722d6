<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; line-height: 1.6; }
      .pyramid-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: bold; fill: white; }
      .keyword-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 22px; fill: white; }
      .purpose-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; fill: white; }
      .percentage-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: white; }
      .pyramid-bg { fill: #f8f9fa; stroke: #e9ecef; stroke-width: 3; rx: 20; }
    </style>
    <defs>
      <linearGradient id="base-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
        <stop offset="0%" style="stop-color:#4caf50;stop-opacity:0.9" />
        <stop offset="100%" style="stop-color:#2e7d32;stop-opacity:1" />
      </linearGradient>
      <linearGradient id="middle-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
        <stop offset="0%" style="stop-color:#ff9800;stop-opacity:0.9" />
        <stop offset="100%" style="stop-color:#e65100;stop-opacity:1" />
      </linearGradient>
      <linearGradient id="top-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
        <stop offset="0%" style="stop-color:#e74c3c;stop-opacity:0.9" />
        <stop offset="100%" style="stop-color:#c62828;stop-opacity:1" />
      </linearGradient>
    </defs>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">私域内容"金字塔"模型</text>
  
  <!-- 主要内容区域 -->
  <rect x="100" y="150" width="1720" height="850" class="pyramid-bg"/>
  
  <!-- 金字塔底层：价值型内容 (70%) -->
  <polygon points="300,700 1300,700 1200,500 400,500" fill="url(#base-gradient)"/>
  <text x="850" y="570" text-anchor="middle" class="pyramid-text">塔基 (占比约70%)</text>
  <text x="850" y="610" text-anchor="middle" class="pyramid-text">价值型内容</text>
  <text x="850" y="650" text-anchor="middle" class="keyword-text">关键词：利他、专业、有用</text>
  <text x="850" y="680" text-anchor="middle" class="purpose-text">目的：建立信任，塑造专家形象</text>
  
  <!-- 金字塔中层：互动型/关系型内容 (20%) -->
  <polygon points="400,500 1200,500 1100,350 500,350" fill="url(#middle-gradient)"/>
  <text x="800" y="400" text-anchor="middle" class="pyramid-text">塔身 (占比约20%)</text>
  <text x="800" y="430" text-anchor="middle" class="pyramid-text">互动型/关系型内容</text>
  <text x="800" y="460" text-anchor="middle" class="keyword-text">关键词：有趣、共鸣、有温度</text>
  <text x="800" y="485" text-anchor="middle" class="purpose-text">目的：拉近关系，活跃气氛</text>
  
  <!-- 金字塔顶层：营销型内容 (10%) -->
  <polygon points="500,350 1100,350 900,200 700,200" fill="url(#top-gradient)"/>
  <text x="800" y="250" text-anchor="middle" class="pyramid-text">塔尖 (占比约10%)</text>
  <text x="800" y="280" text-anchor="middle" class="pyramid-text">营销型内容</text>
  <text x="800" y="310" text-anchor="middle" class="keyword-text">关键词：稀缺、优惠、转化</text>
  <text x="800" y="335" text-anchor="middle" class="purpose-text">目的：促进成交（必须克制！）</text>
  
  <!-- 右侧详细说明 -->
  <rect x="1350" y="200" width="420" height="700" fill="#f1f2f6" stroke="#ddd" stroke-width="2" rx="15"/>
  
  <!-- 价值型内容说明 -->
  <rect x="1370" y="220" width="380" height="200" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="10"/>
  <text x="1390" y="250" class="content-text" font-weight="bold" fill="#2e7d32">价值型内容示例：</text>
  <text x="1390" y="280" class="content-text" font-size="20px" fill="#2e7d32">• 通信知识科普</text>
  <text x="1390" y="305" class="content-text" font-size="20px" fill="#2e7d32">• 省钱使用技巧</text>
  <text x="1390" y="330" class="content-text" font-size="20px" fill="#2e7d32">• 故障解决方案</text>
  <text x="1390" y="355" class="content-text" font-size="20px" fill="#2e7d32">• 行业资讯分享</text>
  <text x="1390" y="380" class="content-text" font-size="20px" fill="#2e7d32">• 政策解读说明</text>
  <text x="1390" y="405" class="content-text" font-size="18px" fill="#2e7d32">目标：让用户觉得"有用"</text>
  
  <!-- 互动型内容说明 -->
  <rect x="1370" y="440" width="380" height="200" fill="#fff3e0" stroke="#ff9800" stroke-width="2" rx="10"/>
  <text x="1390" y="470" class="content-text" font-weight="bold" fill="#e65100">互动型内容示例：</text>
  <text x="1390" y="500" class="content-text" font-size="20px" fill="#e65100">• 生活小趣事分享</text>
  <text x="1390" y="525" class="content-text" font-size="20px" fill="#e65100">• 节日祝福问候</text>
  <text x="1390" y="550" class="content-text" font-size="20px" fill="#e65100">• 有奖互动活动</text>
  <text x="1390" y="575" class="content-text" font-size="20px" fill="#e65100">• 用户故事征集</text>
  <text x="1390" y="600" class="content-text" font-size="20px" fill="#e65100">• 话题讨论引导</text>
  <text x="1390" y="625" class="content-text" font-size="18px" fill="#e65100">目标：让用户觉得"有趣"</text>
  
  <!-- 营销型内容说明 -->
  <rect x="1370" y="660" width="380" height="200" fill="#ffebee" stroke="#e74c3c" stroke-width="2" rx="10"/>
  <text x="1390" y="690" class="content-text" font-weight="bold" fill="#c62828">营销型内容示例：</text>
  <text x="1390" y="720" class="content-text" font-size="20px" fill="#c62828">• 限时优惠活动</text>
  <text x="1390" y="745" class="content-text" font-size="20px" fill="#c62828">• 新产品推荐</text>
  <text x="1390" y="770" class="content-text" font-size="20px" fill="#c62828">• 套餐升级建议</text>
  <text x="1390" y="795" class="content-text" font-size="20px" fill="#c62828">• 增值服务介绍</text>
  <text x="1390" y="820" class="content-text" font-size="18px" fill="#c62828">目标：让用户"买单"</text>
  <text x="1390" y="845" class="content-text" font-size="16px" fill="#c62828" font-weight="bold">⚠️ 必须克制，过多会引起反感</text>
  
  <!-- 左侧比例标识 -->
  <text x="200" y="600" text-anchor="middle" class="percentage-text" fill="#4caf50">70%</text>
  <text x="200" y="425" text-anchor="middle" class="percentage-text" fill="#ff9800">20%</text>
  <text x="200" y="275" text-anchor="middle" class="percentage-text" fill="#e74c3c">10%</text>
  
  <!-- 装饰性元素 -->
  <circle cx="50" cy="500" r="30" fill="#4caf50" opacity="0.3"/>
  <text x="50" y="510" text-anchor="middle" class="content-text" fill="#2e7d32" font-size="16px">价值</text>
  
  <circle cx="1870" cy="500" r="30" fill="#e74c3c" opacity="0.3"/>
  <text x="1870" y="510" text-anchor="middle" class="content-text" fill="#c62828" font-size="16px">营销</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 150 Q 200 130 300 150" stroke="#4caf50" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1620 150 Q 1720 130 1820 150" stroke="#e74c3c" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
