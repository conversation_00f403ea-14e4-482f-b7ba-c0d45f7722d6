<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #2c3e50; line-height: 1.6; }
      .bridge-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #2196f3; }
      .main-bg { fill: #f8f9fa; stroke: #e9ecef; stroke-width: 3; rx: 20; }
      .day2-bg { fill: #e3f2fd; stroke: #2196f3; stroke-width: 3; rx: 15; }
      .day3-bg { fill: #e8f5e8; stroke: #4caf50; stroke-width: 3; rx: 15; }
      .bridge-bg { fill: #fff3e0; stroke: #ff9800; stroke-width: 3; rx: 10; }
      .bridge-structure { fill: #8d6e63; opacity: 0.8; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">连接"体验"与"价值"的桥梁</text>
  
  <!-- 主要内容区域 -->
  <rect x="100" y="150" width="1720" height="750" class="main-bg"/>
  
  <!-- 桥的一端：Day 2 核心产出 -->
  <rect x="200" y="250" width="400" height="300" class="day2-bg"/>
  
  <!-- 旅程地图图标 -->
  <rect x="250" y="300" width="120" height="80" fill="#2196f3" rx="10"/>
  <text x="310" y="345" text-anchor="middle" class="content-text" fill="white" font-size="20px">地图</text>
  
  <text x="400" y="300" text-anchor="middle" class="subtitle" fill="#1565c0">Day 2 核心产出</text>
  
  <text x="220" y="420" class="content-text" fill="#1565c0">图标：客户旅程地图</text>
  <text x="220" y="460" class="content-text" fill="#1565c0">文字：识别了客户的</text>
  <text x="240" y="490" class="content-text" fill="#1565c0">"痛点"与"爽点"</text>
  
  <!-- 桥的另一端：Day 3 核心目标 -->
  <rect x="1320" y="250" width="400" height="300" class="day3-bg"/>
  
  <!-- 增长曲线图标 -->
  <rect x="1370" y="300" width="120" height="80" fill="#4caf50" rx="10"/>
  <path d="M 1380 360 Q 1420 340 1460 320" stroke="white" stroke-width="4" fill="none"/>
  <circle cx="1460" cy="320" r="5" fill="white"/>
  
  <text x="1520" y="300" text-anchor="middle" class="subtitle" fill="#2e7d32">Day 3 核心目标</text>
  
  <text x="1340" y="420" class="content-text" fill="#2e7d32">图标：增长曲线图(ARPU/LTV)</text>
  <text x="1340" y="460" class="content-text" fill="#2e7d32">文字：实现可衡量的</text>
  <text x="1360" y="490" class="content-text" fill="#2e7d32">"商业增长"</text>
  
  <!-- 桥梁结构 -->
  <rect x="600" y="350" width="720" height="100" class="bridge-bg"/>
  
  <!-- 桥梁支撑结构 -->
  <rect x="650" y="300" width="20" height="150" class="bridge-structure"/>
  <rect x="750" y="280" width="20" height="170" class="bridge-structure"/>
  <rect x="850" y="270" width="20" height="180" class="bridge-structure"/>
  <rect x="950" y="270" width="20" height="180" class="bridge-structure"/>
  <rect x="1050" y="280" width="20" height="170" class="bridge-structure"/>
  <rect x="1150" y="300" width="20" height="150" class="bridge-structure"/>
  <rect x="1250" y="320" width="20" height="130" class="bridge-structure"/>
  
  <!-- 桥梁缆绳 -->
  <path d="M 650 300 Q 850 250 1050 280 Q 1150 290 1250 320" stroke="#8d6e63" stroke-width="3" fill="none"/>
  <path d="M 670 310 Q 870 260 1070 290 Q 1170 300 1270 330" stroke="#8d6e63" stroke-width="3" fill="none"/>
  
  <!-- 桥的本身文字 -->
  <text x="960" y="390" text-anchor="middle" class="bridge-text">数据驱动</text>
  <text x="960" y="430" text-anchor="middle" class="bridge-text">(Data-Driven)</text>
  
  <!-- 连接线 -->
  <line x1="600" y1="400" x2="650" y2="400" stroke="#7f8c8d" stroke-width="4"/>
  <line x1="1270" y1="400" x2="1320" y2="400" stroke="#7f8c8d" stroke-width="4"/>
  
  <!-- 核心理念说明 -->
  <rect x="200" y="600" width="1520" height="200" fill="#fff3cd" stroke="#ffc107" stroke-width="3" rx="15"/>
  <text x="250" y="650" class="subtitle" fill="#f57f17">核心理念：</text>
  <text x="250" y="690" class="content-text">连接"体验端"和"价值端"的桥梁是"数据驱动"</text>
  <text x="250" y="730" class="content-text">我们需要用数据来证明，我们对体验的每一次优化，</text>
  <text x="250" y="770" class="content-text">都对最终的商业结果产生了积极的影响</text>
  
  <!-- 装饰性元素 -->
  <circle cx="50" cy="500" r="30" fill="#2196f3" opacity="0.3"/>
  <text x="50" y="510" text-anchor="middle" class="content-text" fill="#1565c0" font-size="16px">体验</text>
  
  <circle cx="1870" cy="500" r="30" fill="#4caf50" opacity="0.3"/>
  <text x="1870" y="510" text-anchor="middle" class="content-text" fill="#2e7d32" font-size="16px">价值</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 150 Q 200 130 300 150" stroke="#2196f3" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1620 150 Q 1720 130 1820 150" stroke="#4caf50" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
