<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #2c3e50; line-height: 1.6; }
      .task-bg { fill: #fff3cd; stroke: #ffc107; stroke-width: 3; rx: 15; }
      .topic-bg { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; rx: 10; }
      .purpose-bg { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; rx: 10; }
      .thinker-bg { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; rx: 20; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">今日"家庭作业"</text>
  
  <!-- 思考者剪影 -->
  <rect x="150" y="150" width="300" height="400" class="thinker-bg"/>
  <circle cx="300" cy="280" r="60" fill="#9c27b0" opacity="0.3"/>
  <ellipse cx="300" cy="380" rx="40" ry="80" fill="#9c27b0" opacity="0.3"/>
  <text x="300" y="480" text-anchor="middle" class="content-text" fill="#6a1b9a">思考者</text>
  
  <!-- 任务区域 -->
  <rect x="500" y="150" width="1270" height="400" class="task-bg"/>
  <text x="550" y="200" class="subtitle">任务：一次5分钟的安静思考</text>
  
  <!-- 题目区域 -->
  <rect x="550" y="240" width="1170" height="280" class="topic-bg"/>
  <text x="580" y="280" class="subtitle" fill="#1565c0">题目：</text>
  
  <text x="600" y="330" class="content-text">• 回想一位你近期服务过的、印象最深刻的客户</text>
  <text x="620" y="370" class="content-text">（可以是让你感动的，也可以是让你头疼的）</text>
  
  <text x="600" y="420" class="content-text">• 尝试用今天学的用户画像方法，在你的脑海里，</text>
  <text x="620" y="460" class="content-text">为他描绘一个简单的Persona</text>
  
  <text x="600" y="500" class="content-text">• 他/她叫什么？有什么样的故事？</text>
  <text x="620" y="540" class="content-text">他/她最大的痛点和烦恼是什么？</text>
  
  <!-- 目的区域 -->
  <rect x="150" y="600" width="1620" height="200" class="purpose-bg"/>
  <text x="200" y="650" class="subtitle" fill="#2e7d32">目的：</text>
  <text x="200" y="700" class="content-text">将今日所学，与真实工作，建立最短的连接。</text>
  <text x="200" y="740" class="content-text">当你开始真正地"思考"你的客户时，你的运营思维，就已经在发生质变了。</text>
  
  <!-- 提示区域 -->
  <rect x="150" y="840" width="1620" height="180" fill="#f1f2f6" stroke="#ddd" stroke-width="2" rx="10"/>
  <text x="200" y="890" class="subtitle" fill="#7f8c8d">💡 温馨提示：</text>
  <text x="250" y="930" class="content-text" fill="#7f8c8d">这个作业不需要动笔写任何东西，只需要在回酒店的路上或睡觉前，</text>
  <text x="250" y="970" class="content-text" fill="#7f8c8d">花五分钟时间进行一次安静的思考即可。</text>
  
  <!-- 装饰性元素 -->
  <circle cx="1750" cy="300" r="40" fill="#ffc107" opacity="0.3"/>
  <text x="1750" y="310" text-anchor="middle" class="content-text" fill="#f57f17" font-size="18px">思考</text>
  
  <circle cx="1750" cy="450" r="40" fill="#2196f3" opacity="0.3"/>
  <text x="1750" y="460" text-anchor="middle" class="content-text" fill="#1565c0" font-size="18px">连接</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 150 150 Q 250 130 350 150" stroke="#9c27b0" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1620 150 Q 1720 130 1820 150" stroke="#ffc107" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
