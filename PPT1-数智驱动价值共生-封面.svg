<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 科技感背景装饰 -->
  <defs>
    <linearGradient id="techGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0066cc;stop-opacity:0.1"/>
      <stop offset="100%" style="stop-color:#003d7a;stop-opacity:0.05"/>
    </linearGradient>
  </defs>
  
  <!-- 弧线装饰元素 -->
  <path d="M 0 200 Q 400 100 800 200 T 1600 200 Q 1800 150 1920 200 L 1920 0 L 0 0 Z" fill="url(#techGradient)"/>
  <path d="M 0 1080 Q 320 980 640 1080 T 1280 1080 Q 1600 1030 1920 1080 L 1920 880 Q 1600 930 1280 880 T 640 880 Q 320 930 0 880 Z" fill="url(#techGradient)"/>
  
  <!-- 数据流装饰线条 -->
  <g stroke="#0066cc" stroke-width="2" fill="none" opacity="0.3">
    <path d="M 100 300 Q 300 250 500 300 T 900 300"/>
    <path d="M 1020 400 Q 1220 350 1420 400 T 1820 400"/>
    <path d="M 200 700 Q 400 650 600 700 T 1000 700"/>
    <path d="M 1120 800 Q 1320 750 1520 800 T 1820 800"/>
  </g>
  
  <!-- 陕西移动Logo区域 -->
  <circle cx="1700" cy="200" r="80" fill="#0066cc" opacity="0.1"/>
  <text x="1700" y="210" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#0066cc">陕西移动</text>
  
  <!-- 主标题 -->
  <text x="960" y="450" text-anchor="middle" font-family="Microsoft YaHei" font-size="88" font-weight="bold" fill="#003d7a">数智驱动·价值共生</text>
  
  <!-- 副标题 -->
  <text x="960" y="550" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="normal" fill="#0066cc">陕西移动客户精细化运营与渠道价值提升实战</text>
  
  <!-- 装饰弧线 -->
  <path d="M 400 600 Q 960 650 1520 600" stroke="#0066cc" stroke-width="3" fill="none" opacity="0.6"/>
  
  <!-- 底部信息 -->
  <text x="960" y="850" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" fill="#666666">主讲培训师：XXX</text>
  <text x="960" y="900" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" fill="#666666">日期：2025年X月X日</text>
  
  <!-- 科技点缀元素 -->
  <g fill="#0066cc" opacity="0.2">
    <circle cx="200" cy="150" r="8"/>
    <circle cx="1720" cy="950" r="12"/>
    <circle cx="150" cy="950" r="6"/>
    <circle cx="1800" cy="100" r="10"/>
  </g>
</svg>
