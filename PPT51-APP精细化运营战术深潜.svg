<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #2c3e50; line-height: 1.6; }
      .question-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #e74c3c; font-weight: bold; }
      .app-bg { fill: #f8f9fa; stroke: #e9ecef; stroke-width: 3; rx: 20; }
      .question-bg { fill: #fff3cd; stroke: #ffc107; stroke-width: 3; rx: 15; }
      .phone-frame { fill: #333; stroke: #222; stroke-width: 3; rx: 25; }
      .screen { fill: #ffffff; stroke: #ddd; stroke-width: 1; rx: 15; }
      .magnifier { fill: #e74c3c; opacity: 0.8; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">战术深潜：中国移动APP精细化运营</text>
  
  <!-- 副标题 -->
  <text x="960" y="140" text-anchor="middle" class="subtitle">从"装机量思维"到"活跃度思维"的跨越</text>
  
  <!-- APP界面背景 -->
  <rect x="150" y="200" width="600" height="600" class="app-bg"/>
  
  <!-- 手机框架 -->
  <rect x="200" y="250" width="300" height="500" class="phone-frame"/>
  <rect x="220" y="280" width="260" height="440" class="screen"/>
  
  <!-- APP界面元素 -->
  <rect x="240" y="300" width="220" height="40" fill="#2196f3" rx="5"/>
  <text x="350" y="325" text-anchor="middle" class="content-text" fill="white" font-size="18px">中国移动APP</text>
  
  <!-- 功能模块 -->
  <rect x="250" y="360" width="80" height="60" fill="#e3f2fd" rx="8"/>
  <text x="290" y="395" text-anchor="middle" class="content-text" font-size="14px">话费</text>
  
  <rect x="340" y="360" width="80" height="60" fill="#e8f5e8" rx="8"/>
  <text x="380" y="395" text-anchor="middle" class="content-text" font-size="14px">流量</text>
  
  <rect x="250" y="430" width="80" height="60" fill="#fff3e0" rx="8"/>
  <text x="290" y="465" text-anchor="middle" class="content-text" font-size="14px">套餐</text>
  
  <rect x="340" y="430" width="80" height="60" fill="#fce4ec" rx="8"/>
  <text x="380" y="465" text-anchor="middle" class="content-text" font-size="14px">服务</text>
  
  <!-- 放大镜 -->
  <circle cx="600" cy="400" r="80" class="magnifier"/>
  <circle cx="600" cy="400" r="60" fill="none" stroke="white" stroke-width="8"/>
  <line x1="650" y1="450" x2="700" y2="500" stroke="white" stroke-width="8" stroke-linecap="round"/>
  
  <!-- 核心问题区域 -->
  <rect x="800" y="200" width="1000" height="600" class="question-bg"/>
  <text x="850" y="260" class="subtitle" fill="#f57f17">核心问题：</text>
  
  <!-- 问题1 -->
  <rect x="850" y="300" width="900" height="80" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" rx="10"/>
  <text x="1300" y="350" text-anchor="middle" class="question-text" fill="#1565c0">用户为什么打开我们的APP？</text>
  
  <!-- 问题2 -->
  <rect x="850" y="400" width="900" height="80" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="10"/>
  <text x="1300" y="450" text-anchor="middle" class="question-text" fill="#2e7d32">我们如何让他更频繁地打开？</text>
  
  <!-- 问题3 -->
  <rect x="850" y="500" width="900" height="80" fill="#ffebee" stroke="#e74c3c" stroke-width="2" rx="10"/>
  <text x="1300" y="550" text-anchor="middle" class="question-text">我们如何让他在里面停留得更久？</text>
  
  <!-- 思维转变说明 -->
  <rect x="850" y="620" width="900" height="140" fill="#f1f2f6" stroke="#7f8c8d" stroke-width="2" rx="10"/>
  <text x="900" y="660" class="content-text" font-weight="bold" fill="#7f8c8d">思维转变：</text>
  <text x="920" y="690" class="content-text" font-size="22px" fill="#7f8c8d">从关注"下载量"到关注"活跃度"</text>
  <text x="920" y="720" class="content-text" font-size="22px" fill="#7f8c8d">从追求"装机量"到追求"使用频次"</text>
  <text x="920" y="750" class="content-text" font-size="22px" fill="#7f8c8d">从重视"功能完整"到重视"用户体验"</text>
  
  <!-- 底部关键指标 -->
  <rect x="150" y="850" width="1620" height="180" fill="#e8f5e8" stroke="#4caf50" stroke-width="3" rx="15"/>
  <text x="200" y="900" class="subtitle" fill="#2e7d32">关键运营指标：</text>
  <text x="250" y="950" class="content-text">• MAU (月活跃用户数) - 衡量APP健康度的核心指标</text>
  <text x="250" y="990" class="content-text">• DAU (日活跃用户数) - 反映用户粘性和使用习惯</text>
  
  <!-- 装饰性元素 -->
  <circle cx="100" cy="500" r="40" fill="#2196f3" opacity="0.3"/>
  <text x="100" y="510" text-anchor="middle" class="content-text" fill="#1565c0" font-size="18px">APP</text>
  
  <circle cx="1820" cy="500" r="40" fill="#e74c3c" opacity="0.3"/>
  <text x="1820" y="510" text-anchor="middle" class="content-text" fill="#c62828" font-size="16px">活跃度</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 150 200 Q 250 180 350 200" stroke="#2196f3" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1650 200 Q 1750 180 1850 200" stroke="#e74c3c" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
