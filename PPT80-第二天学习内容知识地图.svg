<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .center-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .branch-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #34495e; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 22px; fill: #2c3e50; line-height: 1.6; }
      .center-bg { fill: #e74c3c; stroke: #c62828; stroke-width: 3; rx: 20; }
      .weapon-bg { fill: #3498db; stroke: #2980b9; stroke-width: 2; rx: 15; }
      .method-bg { fill: #f39c12; stroke: #e67e22; stroke-width: 2; rx: 15; }
      .connection-bg { fill: #27ae60; stroke: #229954; stroke-width: 2; rx: 15; }
      .connection-line { stroke: #7f8c8d; stroke-width: 3; fill: none; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">第二天学习内容"知识地图"</text>
  
  <!-- 中心点 -->
  <ellipse cx="960" cy="350" rx="200" ry="80" class="center-bg"/>
  <text x="960" y="340" text-anchor="middle" class="content-text" fill="white" font-weight="bold">战术攻坚</text>
  <text x="960" y="370" text-anchor="middle" class="content-text" fill="white">(Tactical Breakthrough)</text>
  
  <!-- 连接线 -->
  <line x1="760" y1="350" x2="450" y2="250" class="connection-line"/>
  <line x1="1160" y1="350" x2="1470" y2="250" class="connection-line"/>
  <line x1="960" y1="430" x2="960" y2="550" class="connection-line"/>
  
  <!-- 第一层分支：两大武器 -->
  <rect x="200" y="150" width="500" height="200" class="weapon-bg"/>
  <text x="450" y="190" text-anchor="middle" class="branch-title" fill="white">两大武器 (Weapons)</text>
  
  <text x="220" y="230" class="content-text" fill="white" font-weight="bold">APP (自助服务大厅)</text>
  <text x="240" y="260" class="content-text" fill="white">核心：提升活跃度 (MAU)</text>
  
  <text x="220" y="300" class="content-text" fill="white" font-weight="bold">企业微信 (私家会客厅)</text>
  <text x="240" y="330" class="content-text" fill="white">核心：建立信任 (Relationship)</text>
  
  <rect x="1220" y="150" width="500" height="200" class="weapon-bg"/>
  <text x="1470" y="190" text-anchor="middle" class="branch-title" fill="white">两大武器 (Weapons)</text>
  
  <text x="1240" y="230" class="content-text" fill="white" font-weight="bold">APP (自助服务大厅)</text>
  <text x="1260" y="260" class="content-text" fill="white">核心：提升活跃度 (MAU)</text>
  
  <text x="1240" y="300" class="content-text" fill="white" font-weight="bold">企业微信 (私家会客厅)</text>
  <text x="1260" y="330" class="content-text" fill="white">核心：建立信任 (Relationship)</text>
  
  <!-- 第二层分支：一套心法 -->
  <rect x="710" y="550" width="500" height="200" class="method-bg"/>
  <text x="960" y="590" text-anchor="middle" class="branch-title" fill="white">一套心法 (Methodology)</text>
  
  <text x="730" y="630" class="content-text" fill="white" font-weight="bold">NPS闭环</text>
  <text x="750" y="660" class="content-text" fill="white">(体验管理仪表盘)</text>
  
  <text x="730" y="700" class="content-text" fill="white" font-weight="bold">客户旅程地图</text>
  <text x="750" y="730" class="content-text" fill="white">(体验诊断手术刀)</text>
  
  <!-- 核心连接 -->
  <rect x="300" y="800" width="1320" height="150" class="connection-bg"/>
  <text x="960" y="840" text-anchor="middle" class="branch-title" fill="white">核心连接：</text>
  <text x="960" y="880" text-anchor="middle" class="content-text" fill="white">用"心法"指导"武器"的使用，</text>
  <text x="960" y="910" text-anchor="middle" class="content-text" fill="white">实现"有温度"的运营</text>
  
  <!-- 装饰性元素 -->
  <circle cx="100" cy="350" r="30" fill="#3498db" opacity="0.3"/>
  <text x="100" y="360" text-anchor="middle" class="content-text" fill="#2980b9" font-size="16px">武器</text>
  
  <circle cx="1820" cy="350" r="30" fill="#f39c12" opacity="0.3"/>
  <text x="1820" y="360" text-anchor="middle" class="content-text" fill="#e67e22" font-size="16px">心法</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 200 120 Q 300 100 400 120" stroke="#3498db" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1520 120 Q 1620 100 1720 120" stroke="#f39c12" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
