<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .main-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #2c3e50; text-anchor: middle; }
      .english-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; fill: #7f8c8d; text-anchor: middle; font-style: italic; }
    </style>
    <defs>
      <radialGradient id="night-gradient" cx="50%" cy="50%" r="70%">
        <stop offset="0%" style="stop-color:#2c3e50;stop-opacity:0.8" />
        <stop offset="100%" style="stop-color:#34495e;stop-opacity:0.3" />
      </radialGradient>
    </defs>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 夜景背景 -->
  <ellipse cx="960" cy="540" rx="900" ry="500" fill="url(#night-gradient)"/>
  
  <!-- 主要文字 -->
  <text x="960" y="450" class="main-text">今日之行，明日之功。</text>
  
  <text x="960" y="550" class="main-text">晚安！</text>
  
  <!-- 英文文字 -->
  <text x="960" y="650" class="english-text">(See You Tomorrow)</text>
  
  <!-- 装饰性星星 -->
  <circle cx="300" cy="200" r="3" fill="#f39c12" opacity="0.8"/>
  <circle cx="1620" cy="200" r="3" fill="#f39c12" opacity="0.8"/>
  <circle cx="200" cy="300" r="2" fill="#f39c12" opacity="0.6"/>
  <circle cx="1720" cy="300" r="2" fill="#f39c12" opacity="0.6"/>
  <circle cx="400" cy="150" r="2" fill="#f39c12" opacity="0.7"/>
  <circle cx="1520" cy="150" r="2" fill="#f39c12" opacity="0.7"/>
  
  <circle cx="250" cy="800" r="3" fill="#f39c12" opacity="0.8"/>
  <circle cx="1670" cy="800" r="3" fill="#f39c12" opacity="0.8"/>
  <circle cx="150" cy="700" r="2" fill="#f39c12" opacity="0.6"/>
  <circle cx="1770" cy="700" r="2" fill="#f39c12" opacity="0.6"/>
  <circle cx="350" cy="850" r="2" fill="#f39c12" opacity="0.7"/>
  <circle cx="1570" cy="850" r="2" fill="#f39c12" opacity="0.7"/>
  
  <!-- 月亮 -->
  <circle cx="1600" cy="250" r="60" fill="#f39c12" opacity="0.3"/>
  <circle cx="1580" cy="230" r="50" fill="#fff" opacity="0.8"/>
  
  <!-- 装饰性建筑轮廓 -->
  <rect x="100" y="900" width="80" height="180" fill="#2c3e50" opacity="0.4"/>
  <rect x="200" y="850" width="60" height="230" fill="#2c3e50" opacity="0.4"/>
  <rect x="280" y="920" width="70" height="160" fill="#2c3e50" opacity="0.4"/>
  
  <rect x="1570" y="900" width="80" height="180" fill="#2c3e50" opacity="0.4"/>
  <rect x="1660" y="850" width="60" height="230" fill="#2c3e50" opacity="0.4"/>
  <rect x="1740" y="920" width="70" height="160" fill="#2c3e50" opacity="0.4"/>
  
  <!-- 建筑物窗户 -->
  <rect x="110" y="920" width="15" height="15" fill="#f39c12" opacity="0.6"/>
  <rect x="130" y="940" width="15" height="15" fill="#f39c12" opacity="0.6"/>
  <rect x="150" y="960" width="15" height="15" fill="#f39c12" opacity="0.6"/>
  
  <rect x="210" y="870" width="15" height="15" fill="#f39c12" opacity="0.6"/>
  <rect x="230" y="890" width="15" height="15" fill="#f39c12" opacity="0.6"/>
  <rect x="210" y="910" width="15" height="15" fill="#f39c12" opacity="0.6"/>
  
  <rect x="1580" y="920" width="15" height="15" fill="#f39c12" opacity="0.6"/>
  <rect x="1600" y="940" width="15" height="15" fill="#f39c12" opacity="0.6"/>
  <rect x="1620" y="960" width="15" height="15" fill="#f39c12" opacity="0.6"/>
  
  <rect x="1670" y="870" width="15" height="15" fill="#f39c12" opacity="0.6"/>
  <rect x="1690" y="890" width="15" height="15" fill="#f39c12" opacity="0.6"/>
  <rect x="1670" y="910" width="15" height="15" fill="#f39c12" opacity="0.6"/>
</svg>
