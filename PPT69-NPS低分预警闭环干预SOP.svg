<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; line-height: 1.6; }
      .step-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 22px; fill: #2c3e50; line-height: 1.5; }
      .step-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: bold; fill: #34495e; }
      .flow-bg { fill: #f8f9fa; stroke: #e9ecef; stroke-width: 3; rx: 20; }
      .step-bg { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; rx: 15; }
      .conclusion-bg { fill: #fff3cd; stroke: #ffc107; stroke-width: 3; rx: 15; }
      .arrow { fill: #e74c3c; }
      .number-circle { fill: #2196f3; stroke: #1976d2; stroke-width: 2; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">从"测量"到"管理"：NPS低分预警与闭环干预SOP</text>
  
  <!-- 流程图背景 -->
  <rect x="100" y="150" width="1720" height="500" class="flow-bg"/>
  
  <!-- 步骤1：实时预警 -->
  <circle cx="200" cy="250" r="25" class="number-circle"/>
  <text x="200" y="258" text-anchor="middle" class="content-text" fill="white" font-weight="bold">1</text>
  
  <rect x="120" y="290" width="160" height="120" class="step-bg"/>
  <text x="200" y="320" text-anchor="middle" class="step-title">实时预警</text>
  <text x="200" y="345" text-anchor="middle" class="step-text">(Trigger)</text>
  <text x="200" y="370" text-anchor="middle" class="step-text" font-size="18px">用户打分0-6分</text>
  <text x="200" y="390" text-anchor="middle" class="step-text" font-size="18px">10分钟内自动</text>
  <text x="200" y="405" text-anchor="middle" class="step-text" font-size="18px">生成预警工单</text>
  
  <!-- 箭头1 -->
  <polygon points="300,340 350,325 350,335 380,335 380,365 350,365 350,375" class="arrow"/>
  
  <!-- 步骤2：精准派单 -->
  <circle cx="480" cy="250" r="25" class="number-circle"/>
  <text x="480" y="258" text-anchor="middle" class="content-text" fill="white" font-weight="bold">2</text>
  
  <rect x="400" y="290" width="160" height="120" class="step-bg"/>
  <text x="480" y="320" text-anchor="middle" class="step-title">精准派单</text>
  <text x="480" y="345" text-anchor="middle" class="step-text">(Dispatch)</text>
  <text x="480" y="370" text-anchor="middle" class="step-text" font-size="18px">自动派发给</text>
  <text x="480" y="390" text-anchor="middle" class="step-text" font-size="18px">归属地市/营业厅</text>
  <text x="480" y="405" text-anchor="middle" class="step-text" font-size="18px">客户经理主管</text>
  
  <!-- 箭头2 -->
  <polygon points="580,340 630,325 630,335 660,335 660,365 630,365 630,375" class="arrow"/>
  
  <!-- 步骤3：快速响应 -->
  <circle cx="760" cy="250" r="25" class="number-circle"/>
  <text x="760" y="258" text-anchor="middle" class="content-text" fill="white" font-weight="bold">3</text>
  
  <rect x="680" y="290" width="160" height="120" class="step-bg"/>
  <text x="760" y="320" text-anchor="middle" class="step-title">快速响应</text>
  <text x="760" y="345" text-anchor="middle" class="step-text">(Follow-up)</text>
  <text x="760" y="370" text-anchor="middle" class="step-text" font-size="18px">主管指派专人</text>
  <text x="760" y="390" text-anchor="middle" class="step-text" font-size="18px">24小时内必须</text>
  <text x="760" y="405" text-anchor="middle" class="step-text" font-size="18px">联系上客户</text>
  
  <!-- 箭头3 -->
  <polygon points="860,340 910,325 910,335 940,335 940,365 910,365 910,375" class="arrow"/>
  
  <!-- 步骤4：解决问题 -->
  <circle cx="1040" cy="250" r="25" class="number-circle"/>
  <text x="1040" y="258" text-anchor="middle" class="content-text" fill="white" font-weight="bold">4</text>
  
  <rect x="960" y="290" width="160" height="120" class="step-bg"/>
  <text x="1040" y="320" text-anchor="middle" class="step-title">解决问题</text>
  <text x="1040" y="345" text-anchor="middle" class="step-text">(Solve)</text>
  <text x="1040" y="370" text-anchor="middle" class="step-text" font-size="18px">倾听、安抚</text>
  <text x="1040" y="390" text-anchor="middle" class="step-text" font-size="18px">探寻根源</text>
  <text x="1040" y="405" text-anchor="middle" class="step-text" font-size="18px">提供解决方案</text>
  
  <!-- 箭头4 -->
  <polygon points="1140,340 1190,325 1190,335 1220,335 1220,365 1190,365 1190,375" class="arrow"/>
  
  <!-- 步骤5：复盘归因 -->
  <circle cx="1320" cy="250" r="25" class="number-circle"/>
  <text x="1320" y="258" text-anchor="middle" class="content-text" fill="white" font-weight="bold">5</text>
  
  <rect x="1240" y="290" width="160" height="120" class="step-bg"/>
  <text x="1320" y="320" text-anchor="middle" class="step-title">复盘归因</text>
  <text x="1320" y="345" text-anchor="middle" class="step-text">(Analyze)</text>
  <text x="1320" y="370" text-anchor="middle" class="step-text" font-size="18px">记录真实原因</text>
  <text x="1320" y="390" text-anchor="middle" class="step-text" font-size="18px">每周复盘</text>
  <text x="1320" y="405" text-anchor="middle" class="step-text" font-size="18px">推动流程优化</text>
  
  <!-- 闭环标识 -->
  <rect x="1450" y="290" width="200" height="120" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="10"/>
  <text x="1550" y="330" text-anchor="middle" class="step-title" fill="#2e7d32">完美闭环</text>
  <text x="1550" y="355" text-anchor="middle" class="step-text" font-size="18px" fill="#2e7d32">把每一次"差评"</text>
  <text x="1550" y="375" text-anchor="middle" class="step-text" font-size="18px" fill="#2e7d32">转化为宝贵的</text>
  <text x="1550" y="395" text-anchor="middle" class="step-text" font-size="18px" fill="#2e7d32">"改进机会"</text>
  
  <!-- 核心理念 -->
  <rect x="200" y="700" width="1520" height="200" class="conclusion-bg"/>
  <text x="250" y="750" class="subtitle" fill="#f57f17">核心理念：</text>
  <text x="250" y="790" class="content-text">把每一次"差评"，都转化为一次宝贵的"改进机会"</text>
  <text x="250" y="830" class="content-text">速度是诚意的第一体现！在客户最生气的时候快速响应，本身就是最好的安抚</text>
  <text x="250" y="870" class="content-text">通过闭环管理，让每一次客户的"抱怨"都成为企业进步的动力</text>
  
  <!-- 装饰性元素 -->
  <circle cx="50" cy="400" r="30" fill="#2196f3" opacity="0.3"/>
  <text x="50" y="410" text-anchor="middle" class="content-text" fill="#1565c0" font-size="16px">闭环</text>
  
  <circle cx="1870" cy="400" r="30" fill="#4caf50" opacity="0.3"/>
  <text x="1870" y="410" text-anchor="middle" class="content-text" fill="#2e7d32" font-size="16px">改进</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 150 Q 200 130 300 150" stroke="#2196f3" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1620 150 Q 1720 130 1820 150" stroke="#4caf50" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
