<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; line-height: 1.6; }
      .strategy-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: bold; fill: #34495e; }
      .quote-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 22px; fill: #e74c3c; font-style: italic; }
      .strategy-bg { fill: #f8f9fa; stroke: #e9ecef; stroke-width: 3; rx: 15; }
      .strategy1-bg { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; rx: 10; }
      .strategy2-bg { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; rx: 10; }
      .strategy3-bg { fill: #fff3e0; stroke: #ff9800; stroke-width: 2; rx: 10; }
      .strategy4-bg { fill: #fce4ec; stroke: #e91e63; stroke-width: 2; rx: 10; }
      .icon-circle { stroke-width: 3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">私域第一步：如何把客户"请"进你的鱼塘？</text>
  
  <!-- 主要内容区域 -->
  <rect x="100" y="150" width="1720" height="850" class="strategy-bg"/>
  
  <!-- 策略1：厅店引流 -->
  <rect x="150" y="200" width="800" height="180" class="strategy1-bg"/>
  <circle cx="220" cy="250" r="30" fill="#2196f3" class="icon-circle"/>
  <text x="220" y="260" text-anchor="middle" class="content-text" fill="white" font-weight="bold">1</text>
  
  <text x="280" y="240" class="strategy-title" fill="#1565c0">厅店引流</text>
  <text x="280" y="270" class="content-text">利用线下服务触点，面对面邀请</text>
  <text x="280" y="310" class="quote-text">"加我，您就有了专属管家。"</text>
  <text x="280" y="340" class="content-text" font-size="20px">优势：信任度高，转化率高，可以现场演示价值</text>
  
  <!-- 策略2：维系引流 -->
  <rect x="1020" y="200" width="800" height="180" class="strategy2-bg"/>
  <circle cx="1090" cy="250" r="30" fill="#4caf50" class="icon-circle"/>
  <text x="1090" y="260" text-anchor="middle" class="content-text" fill="white" font-weight="bold">2</text>
  
  <text x="1150" y="240" class="strategy-title" fill="#2e7d32">维系引流</text>
  <text x="1150" y="270" class="content-text">在10086通话、装维服务后，顺势添加</text>
  <text x="1150" y="310" class="quote-text">"为了以后更好地为您服务。"</text>
  <text x="1150" y="340" class="content-text" font-size="20px">优势：时机自然，客户接受度高，服务延续性强</text>
  
  <!-- 策略3：活动引流 -->
  <rect x="150" y="420" width="800" height="180" class="strategy3-bg"/>
  <circle cx="220" cy="470" r="30" fill="#ff9800" class="icon-circle"/>
  <text x="220" y="480" text-anchor="middle" class="content-text" fill="white" font-weight="bold">3</text>
  
  <text x="280" y="460" class="strategy-title" fill="#e65100">活动引流</text>
  <text x="280" y="490" class="content-text">用专属福利作为"诱饵"</text>
  <text x="280" y="530" class="quote-text">"添加企微，额外再送视频月卡。"</text>
  <text x="280" y="560" class="content-text" font-size="20px">优势：吸引力强，快速获客，可量化效果</text>
  
  <!-- 策略4：内容引流 -->
  <rect x="1020" y="420" width="800" height="180" class="strategy4-bg"/>
  <circle cx="1090" cy="470" r="30" fill="#e91e63" class="icon-circle"/>
  <text x="1090" y="480" text-anchor="middle" class="content-text" fill="white" font-weight="bold">4</text>
  
  <text x="1150" y="460" class="strategy-title" fill="#c2185b">内容引流</text>
  <text x="1150" y="490" class="content-text">用专业价值吸引关注</text>
  <text x="1150" y="530" class="quote-text">"想了解更多省钱技巧？加我详聊。"</text>
  <text x="1150" y="560" class="content-text" font-size="20px">优势：质量高，粘性强，建立专业形象</text>
  
  <!-- 引流成功的关键要素 -->
  <rect x="200" y="650" width="1520" height="300" fill="#fff3cd" stroke="#ffc107" stroke-width="3" rx="15"/>
  <text x="250" y="700" class="subtitle" fill="#f57f17">引流成功的关键要素：</text>
  
  <text x="300" y="750" class="content-text" font-weight="bold">1. 给客户一个"无法拒绝"的理由</text>
  <text x="320" y="780" class="content-text">• 明确的价值承诺：专属服务、独家优惠、专业指导</text>
  <text x="320" y="810" class="content-text">• 即时的利益获得：立即享受、马上体验、现在就有</text>
  
  <text x="300" y="860" class="content-text" font-weight="bold">2. 选择合适的时机和场景</text>
  <text x="320" y="890" class="content-text">• 客户有需求的时候：问题解决后、服务体验好时、有疑问时</text>
  <text x="320" y="920" class="content-text">• 客户心情好的时候：满意度高、获得优惠、问题解决</text>
  
  <!-- 装饰性元素 -->
  <circle cx="50" cy="400" r="30" fill="#2196f3" opacity="0.3"/>
  <text x="50" y="410" text-anchor="middle" class="content-text" fill="#1565c0" font-size="16px">引流</text>
  
  <circle cx="1870" cy="400" r="30" fill="#4caf50" opacity="0.3"/>
  <text x="1870" y="410" text-anchor="middle" class="content-text" fill="#2e7d32" font-size="16px">鱼塘</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 150 Q 200 130 300 150" stroke="#2196f3" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1620 150 Q 1720 130 1820 150" stroke="#4caf50" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
