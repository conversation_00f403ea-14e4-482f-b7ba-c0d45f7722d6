<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <text x="960" y="100" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="#003d7a">【练习】绘制你地市的"客户资产负债表"</text>
  
  <!-- 任务说明框 -->
  <rect x="200" y="180" width="1520" height="300" rx="20" fill="#f0f8ff" stroke="#0066cc" stroke-width="3"/>
  <text x="960" y="230" text-anchor="middle" font-family="Microsoft YaHei" font-size="42" font-weight="bold" fill="#0066cc">任务</text>
  
  <text x="250" y="280" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#003d7a">请以小组为单位，在学员手册P5页，尝试为你所在的地市，</text>
  <text x="250" y="320" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#003d7a">填写这张"客户资产负债表"的简版。</text>
  
  <!-- 要求 -->
  <text x="960" y="380" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#ff6600">要求：</text>
  
  <circle cx="300" cy="420" r="12" fill="#0066cc"/>
  <text x="330" y="430" font-family="Microsoft YaHei" font-size="24" fill="#333">不用追求数据的精确。</text>
  
  <circle cx="300" cy="460" r="12" fill="#0066cc"/>
  <text x="330" y="470" font-family="Microsoft YaHei" font-size="24" fill="#333">重点是描述出各类客户的典型特征。</text>
  
  <circle cx="300" cy="500" r="12" fill="#0066cc"/>
  <text x="330" y="510" font-family="Microsoft YaHei" font-size="24" fill="#333">思考针对每一类客户，我们最应该做什么？</text>
  
  <!-- 时间框 -->
  <rect x="400" y="580" width="1120" height="120" rx="20" fill="#ff6b35" stroke="#ff6b35" stroke-width="3"/>
  <text x="960" y="630" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="white">时间：5分钟</text>
  <text x="960" y="670" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" fill="white">现在开始！</text>
  
  <!-- 装饰元素 -->
  <g fill="#0066cc" opacity="0.3">
    <!-- 表格图标 -->
    <rect x="1600" y="300" width="80" height="60" rx="5" fill="none" stroke="#0066cc" stroke-width="3"/>
    <line x1="1620" y1="300" x2="1620" y2="360" stroke="#0066cc" stroke-width="2"/>
    <line x1="1640" y1="300" x2="1640" y2="360" stroke="#0066cc" stroke-width="2"/>
    <line x1="1660" y1="300" x2="1660" y2="360" stroke="#0066cc" stroke-width="2"/>
    <line x1="1600" y1="320" x2="1680" y2="320" stroke="#0066cc" stroke-width="2"/>
    <line x1="1600" y1="340" x2="1680" y2="340" stroke="#0066cc" stroke-width="2"/>
    
    <!-- 笔图标 -->
    <line x1="1600" y1="400" x2="1650" y2="450" stroke="#0066cc" stroke-width="4"/>
    <polygon points="1645,445 1655,455 1650,460 1640,450" fill="#0066cc"/>
    
    <!-- 时钟图标 -->
    <circle cx="1640" cy="500" r="25" fill="none" stroke="#0066cc" stroke-width="3"/>
    <line x1="1640" y1="485" x2="1640" y2="500" stroke="#0066cc" stroke-width="3"/>
    <line x1="1640" y1="500" x2="1650" y2="510" stroke="#0066cc" stroke-width="3"/>
  </g>
  
  <!-- 底部装饰弧线 -->
  <path d="M 200 750 Q 960 720 1720 750" stroke="#0066cc" stroke-width="2" fill="none" opacity="0.4"/>
  <path d="M 300 800 Q 960 830 1620 800" stroke="#ff6b35" stroke-width="2" fill="none" opacity="0.5"/>
</svg>
