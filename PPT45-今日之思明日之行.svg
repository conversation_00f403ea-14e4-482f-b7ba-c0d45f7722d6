<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .main-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #2c3e50; }
      .english-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; fill: #7f8c8d; font-style: italic; }
      .night-bg { fill: #1a1a2e; }
      .star { fill: #ffd700; }
      .city-building { fill: #16213e; stroke: #0f3460; stroke-width: 2; }
      .window { fill: #ffc107; opacity: 0.8; }
    </style>
    <defs>
      <radialGradient id="moon-gradient" cx="30%" cy="30%" r="70%">
        <stop offset="0%" style="stop-color:#ffd700;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#ffb300;stop-opacity:0.8" />
      </radialGradient>
    </defs>
  </defs>
  
  <!-- 夜空背景 -->
  <rect width="1920" height="1080" class="night-bg"/>
  
  <!-- 星星 -->
  <circle cx="200" cy="150" r="3" class="star"/>
  <circle cx="400" cy="100" r="2" class="star"/>
  <circle cx="600" cy="180" r="3" class="star"/>
  <circle cx="800" cy="120" r="2" class="star"/>
  <circle cx="1000" cy="160" r="3" class="star"/>
  <circle cx="1200" cy="90" r="2" class="star"/>
  <circle cx="1400" cy="140" r="3" class="star"/>
  <circle cx="1600" cy="110" r="2" class="star"/>
  <circle cx="1800" cy="170" r="3" class="star"/>
  
  <circle cx="300" cy="250" r="2" class="star"/>
  <circle cx="500" cy="200" r="3" class="star"/>
  <circle cx="700" cy="280" r="2" class="star"/>
  <circle cx="900" cy="220" r="3" class="star"/>
  <circle cx="1100" cy="260" r="2" class="star"/>
  <circle cx="1300" cy="210" r="3" class="star"/>
  <circle cx="1500" cy="270" r="2" class="star"/>
  <circle cx="1700" cy="230" r="3" class="star"/>
  
  <!-- 月亮 -->
  <circle cx="1600" cy="200" r="80" fill="url(#moon-gradient)"/>
  
  <!-- 城市建筑轮廓 -->
  <!-- 建筑1 -->
  <rect x="100" y="700" width="150" height="380" class="city-building"/>
  <rect x="120" y="720" width="20" height="30" class="window"/>
  <rect x="150" y="720" width="20" height="30" class="window"/>
  <rect x="180" y="720" width="20" height="30" class="window"/>
  <rect x="120" y="780" width="20" height="30" class="window"/>
  <rect x="180" y="780" width="20" height="30" class="window"/>
  
  <!-- 建筑2 -->
  <rect x="300" y="600" width="200" height="480" class="city-building"/>
  <rect x="320" y="630" width="25" height="35" class="window"/>
  <rect x="360" y="630" width="25" height="35" class="window"/>
  <rect x="400" y="630" width="25" height="35" class="window"/>
  <rect x="440" y="630" width="25" height="35" class="window"/>
  <rect x="320" y="700" width="25" height="35" class="window"/>
  <rect x="400" y="700" width="25" height="35" class="window"/>
  
  <!-- 建筑3 -->
  <rect x="550" y="750" width="120" height="330" class="city-building"/>
  <rect x="570" y="780" width="18" height="25" class="window"/>
  <rect x="600" y="780" width="18" height="25" class="window"/>
  <rect x="630" y="780" width="18" height="25" class="window"/>
  
  <!-- 建筑4 -->
  <rect x="720" y="650" width="180" height="430" class="city-building"/>
  <rect x="740" y="680" width="22" height="32" class="window"/>
  <rect x="770" y="680" width="22" height="32" class="window"/>
  <rect x="800" y="680" width="22" height="32" class="window"/>
  <rect x="830" y="680" width="22" height="32" class="window"/>
  <rect x="860" y="680" width="22" height="32" class="window"/>
  
  <!-- 建筑5 -->
  <rect x="950" y="720" width="160" height="360" class="city-building"/>
  <rect x="970" y="750" width="20" height="28" class="window"/>
  <rect x="1000" y="750" width="20" height="28" class="window"/>
  <rect x="1030" y="750" width="20" height="28" class="window"/>
  <rect x="1060" y="750" width="20" height="28" class="window"/>
  
  <!-- 建筑6 -->
  <rect x="1150" y="680" width="140" height="400" class="city-building"/>
  <rect x="1170" y="710" width="18" height="26" class="window"/>
  <rect x="1200" y="710" width="18" height="26" class="window"/>
  <rect x="1230" y="710" width="18" height="26" class="window"/>
  <rect x="1260" y="710" width="18" height="26" class="window"/>
  
  <!-- 建筑7 -->
  <rect x="1330" y="620" width="190" height="460" class="city-building"/>
  <rect x="1350" y="650" width="24" height="34" class="window"/>
  <rect x="1380" y="650" width="24" height="34" class="window"/>
  <rect x="1410" y="650" width="24" height="34" class="window"/>
  <rect x="1440" y="650" width="24" height="34" class="window"/>
  <rect x="1470" y="650" width="24" height="34" class="window"/>
  
  <!-- 建筑8 -->
  <rect x="1560" y="740" width="130" height="340" class="city-building"/>
  <rect x="1580" y="770" width="19" height="27" class="window"/>
  <rect x="1610" y="770" width="19" height="27" class="window"/>
  <rect x="1640" y="770" width="19" height="27" class="window"/>
  
  <!-- 建筑9 -->
  <rect x="1730" y="690" width="170" height="390" class="city-building"/>
  <rect x="1750" y="720" width="21" height="30" class="window"/>
  <rect x="1780" y="720" width="21" height="30" class="window"/>
  <rect x="1810" y="720" width="21" height="30" class="window"/>
  <rect x="1840" y="720" width="21" height="30" class="window"/>
  
  <!-- 主要文字 -->
  <text x="960" y="400" text-anchor="middle" class="main-text" fill="white">今日之思，明日之行。</text>
  
  <!-- 晚安文字 -->
  <text x="960" y="500" text-anchor="middle" class="main-text" fill="white">晚安！</text>
  
  <!-- 英文文字 -->
  <text x="960" y="580" text-anchor="middle" class="english-text">(See You Tomorrow)</text>
</svg>
