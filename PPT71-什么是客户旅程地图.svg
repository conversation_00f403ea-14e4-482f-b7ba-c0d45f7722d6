<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #2c3e50; line-height: 1.6; }
      .definition-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; line-height: 1.5; }
      .essence-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #4caf50; line-height: 1.5; }
      .main-bg { fill: #f8f9fa; stroke: #e9ecef; stroke-width: 3; rx: 20; }
      .definition-bg { fill: #e3f2fd; stroke: #2196f3; stroke-width: 3; rx: 15; }
      .essence-bg { fill: #e8f5e8; stroke: #4caf50; stroke-width: 3; rx: 15; }
      .timeline { stroke: #7f8c8d; stroke-width: 4; }
      .touchpoint { fill: #e74c3c; }
      .emotion-curve { stroke: #ff9800; stroke-width: 3; fill: none; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">什么是客户旅程地图？</text>
  
  <!-- 主要内容区域 -->
  <rect x="100" y="150" width="1720" height="750" class="main-bg"/>
  
  <!-- 旅程地图示意图 -->
  <rect x="200" y="200" width="1520" height="300" fill="#f1f2f6" stroke="#ddd" stroke-width="2" rx="15"/>
  
  <!-- 时间轴 -->
  <line x1="250" y1="350" x2="1670" y2="350" class="timeline"/>
  
  <!-- 触点 -->
  <circle cx="400" cy="350" r="8" class="touchpoint"/>
  <circle cx="700" cy="350" r="8" class="touchpoint"/>
  <circle cx="1000" cy="350" r="8" class="touchpoint"/>
  <circle cx="1300" cy="350" r="8" class="touchpoint"/>
  <circle cx="1600" cy="350" r="8" class="touchpoint"/>
  
  <!-- 行为和想法标注 -->
  <text x="400" y="380" text-anchor="middle" class="definition-text" font-size="16px">行为</text>
  <text x="700" y="380" text-anchor="middle" class="definition-text" font-size="16px">行为</text>
  <text x="1000" y="380" text-anchor="middle" class="definition-text" font-size="16px">行为</text>
  <text x="1300" y="380" text-anchor="middle" class="definition-text" font-size="16px">行为</text>
  <text x="1600" y="380" text-anchor="middle" class="definition-text" font-size="16px">行为</text>
  
  <text x="400" y="400" text-anchor="middle" class="definition-text" font-size="16px">想法</text>
  <text x="700" y="400" text-anchor="middle" class="definition-text" font-size="16px">想法</text>
  <text x="1000" y="400" text-anchor="middle" class="definition-text" font-size="16px">想法</text>
  <text x="1300" y="400" text-anchor="middle" class="definition-text" font-size="16px">想法</text>
  <text x="1600" y="400" text-anchor="middle" class="definition-text" font-size="16px">想法</text>
  
  <!-- 情绪曲线 -->
  <path d="M 250 300 Q 400 280 550 320 Q 700 300 850 290 Q 1000 310 1150 280 Q 1300 270 1450 300 Q 1600 290 1670 280" class="emotion-curve"/>
  
  <!-- 情绪标注 -->
  <text x="250" y="270" class="definition-text" font-size="16px" fill="#ff9800">情绪高低起伏</text>
  
  <!-- 时间轴标注 -->
  <text x="250" y="470" class="definition-text" font-size="16px">时间轴：完整过程</text>
  <text x="1600" y="470" text-anchor="end" class="definition-text" font-size="16px">触点：交互节点</text>
  
  <!-- 核心定义 -->
  <rect x="200" y="530" width="1520" height="120" class="definition-bg"/>
  <text x="250" y="570" class="subtitle" fill="#1565c0">核心定义：</text>
  <text x="250" y="610" class="definition-text">以客户视角为中心，通过可视化的方式，描述一个客户为了达成某个特定目标</text>
  <text x="250" y="635" class="definition-text">（如"办理宽带"），与我们的产品/服务进行交互的完整过程。</text>
  
  <!-- 本质 -->
  <rect x="200" y="670" width="1520" height="120" class="essence-bg"/>
  <text x="250" y="710" class="subtitle" fill="#2e7d32">它的本质：</text>
  <text x="250" y="750" class="essence-text">一个"共情"工具，一个"故事化"的诊断工具</text>
  <text x="250" y="775" class="essence-text">让我们彻底忘掉员工身份，进行一次"灵魂附体"，以客户第一视角体验全过程</text>
  
  <!-- 装饰性元素 -->
  <circle cx="50" cy="500" r="30" fill="#2196f3" opacity="0.3"/>
  <text x="50" y="510" text-anchor="middle" class="content-text" fill="#1565c0" font-size="16px">地图</text>
  
  <circle cx="1870" cy="500" r="30" fill="#4caf50" opacity="0.3"/>
  <text x="1870" y="510" text-anchor="middle" class="content-text" fill="#2e7d32" font-size="16px">共情</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 150 Q 200 130 300 150" stroke="#2196f3" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1620 150 Q 1720 130 1820 150" stroke="#4caf50" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
