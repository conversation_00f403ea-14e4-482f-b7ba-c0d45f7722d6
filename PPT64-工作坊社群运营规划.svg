<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #2c3e50; line-height: 1.6; }
      .task-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; line-height: 1.5; }
      .task-bg { fill: #fff3cd; stroke: #ffc107; stroke-width: 3; rx: 20; }
      .identity-bg { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; rx: 15; }
      .target-bg { fill: #e8f5e8; stroke: #4caf50; stroke-width: 2; rx: 15; }
      .mission-bg { fill: #fce4ec; stroke: #e91e63; stroke-width: 2; rx: 15; }
      .time-bg { fill: #f3e5f5; stroke: #9c27b0; stroke-width: 2; rx: 15; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">【工作坊】社群运营规划</text>
  
  <!-- 主要任务卡区域 -->
  <rect x="200" y="150" width="1520" height="800" class="task-bg"/>
  
  <!-- 身份设定 -->
  <rect x="250" y="200" width="1420" height="100" class="identity-bg"/>
  <text x="300" y="240" class="subtitle" fill="#1565c0">你的身份：</text>
  <text x="300" y="280" class="content-text">一个专业的"社群运营团队"</text>
  
  <!-- 目标客户 -->
  <rect x="250" y="320" width="1420" height="120" class="target-bg"/>
  <text x="300" y="360" class="subtitle" fill="#2e7d32">你的目标客户：</text>
  <text x="300" y="400" class="content-text">"西安市曲江新区的、家里有学龄儿童的、</text>
  <text x="300" y="430" class="content-text">高价值家庭宽带用户"</text>
  
  <!-- 任务要求 -->
  <rect x="250" y="460" width="1420" height="300" class="mission-bg"/>
  <text x="300" y="500" class="subtitle" fill="#c2185b">你的任务：</text>
  
  <text x="350" y="550" class="task-text" font-weight="bold">1. 给你们的社群，取一个响亮、有吸引力的名字，</text>
  <text x="370" y="580" class="task-text">并写下一段50字左右的群规。</text>
  
  <text x="350" y="630" class="task-text" font-weight="bold">2. 参照"内容日历"方法，为你们的社群，</text>
  <text x="370" y="660" class="task-text">规划出第一周（周一到周日）每天要发布的</text>
  <text x="370" y="690" class="task-text">核心内容主题。</text>
  
  <!-- 时间要求 -->
  <rect x="250" y="780" width="1420" height="100" class="time-bg"/>
  <text x="300" y="820" class="subtitle" fill="#7b1fa2">时间：</text>
  <text x="450" y="820" class="subtitle" fill="#7b1fa2" font-size="48px">20分钟</text>
  
  <!-- 思考提示 -->
  <rect x="300" y="900" width="1320" height="40" fill="#f1f2f6" stroke="#ddd" stroke-width="1" rx="8"/>
  <text x="320" y="925" class="task-text" font-size="20px" fill="#7f8c8d">💡 思考：目标用户除了需要稳定网络，还关心什么？孩子教育？周末去哪玩？智能家居？</text>
  
  <!-- 装饰性元素 -->
  <circle cx="100" cy="400" r="40" fill="#ffc107" opacity="0.3"/>
  <text x="100" y="410" text-anchor="middle" class="content-text" fill="#f57f17" font-size="18px">工作坊</text>
  
  <circle cx="1820" cy="400" r="40" fill="#e91e63" opacity="0.3"/>
  <text x="1820" y="410" text-anchor="middle" class="content-text" fill="#c2185b" font-size="18px">规划</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 200 150 Q 300 130 400 150" stroke="#ffc107" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1520 150 Q 1620 130 1720 150" stroke="#e91e63" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
