<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #2c3e50; line-height: 1.6; }
      .question-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; fill: #e74c3c; font-style: italic; }
      .content-bg { fill: #f8f9fa; stroke: #e9ecef; stroke-width: 3; rx: 20; }
      .book-bg { fill: #8d6e63; stroke: #5d4037; stroke-width: 3; rx: 10; }
      .writer-bg { fill: #ecf0f1; stroke: #bdc3c7; stroke-width: 2; }
    </style>
    <defs>
      <linearGradient id="book-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#8d6e63;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#5d4037;stop-opacity:0.8" />
      </linearGradient>
    </defs>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">私域运营的核心：内容为王</text>
  
  <!-- 副标题 -->
  <text x="960" y="140" text-anchor="middle" class="subtitle">如何持续地产出让客户"不反感、想互动、愿买单"的内容？</text>
  
  <!-- 主要内容区域 -->
  <rect x="100" y="200" width="1720" height="700" class="content-bg"/>
  
  <!-- 左侧：书架背景 -->
  <rect x="150" y="250" width="600" height="600" fill="#f1f2f6" stroke="#ddd" stroke-width="2" rx="15"/>
  
  <!-- 书架上的书 -->
  <rect x="180" y="280" width="40" height="200" fill="url(#book-gradient)" rx="3"/>
  <rect x="230" y="280" width="35" height="200" fill="#2196f3" rx="3"/>
  <rect x="275" y="280" width="45" height="200" fill="#4caf50" rx="3"/>
  <rect x="330" y="280" width="38" height="200" fill="#ff9800" rx="3"/>
  <rect x="378" y="280" width="42" height="200" fill="#e91e63" rx="3"/>
  <rect x="430" y="280" width="36" height="200" fill="#9c27b0" rx="3"/>
  <rect x="476" y="280" width="44" height="200" fill="#607d8b" rx="3"/>
  <rect x="530" y="280" width="40" height="200" fill="#795548" rx="3"/>
  <rect x="580" y="280" width="38" height="200" fill="#f44336" rx="3"/>
  <rect x="628" y="280" width="42" height="200" fill="#3f51b5" rx="3"/>
  
  <!-- 第二排书 -->
  <rect x="180" y="500" width="42" height="200" fill="#009688" rx="3"/>
  <rect x="232" y="500" width="38" height="200" fill="#cddc39" rx="3"/>
  <rect x="280" y="500" width="40" height="200" fill="#ffc107" rx="3"/>
  <rect x="330" y="500" width="45" height="200" fill="#673ab7" rx="3"/>
  <rect x="385" y="500" width="35" height="200" fill="#ff5722" rx="3"/>
  <rect x="430" y="500" width="44" height="200" fill="#00bcd4" rx="3"/>
  <rect x="484" y="500" width="36" height="200" fill="#8bc34a" rx="3"/>
  <rect x="530" y="500" width="42" height="200" fill="#ffeb3b" rx="3"/>
  <rect x="582" y="500" width="38" height="200" fill="#e91e63" rx="3"/>
  <rect x="630" y="500" width="40" height="200" fill="#2196f3" rx="3"/>
  
  <!-- 第三排书 -->
  <rect x="180" y="720" width="38" height="100" fill="#4caf50" rx="3"/>
  <rect x="228" y="720" width="44" height="100" fill="#ff9800" rx="3"/>
  <rect x="282" y="720" width="36" height="100" fill="#9c27b0" rx="3"/>
  <rect x="328" y="720" width="42" height="100" fill="#607d8b" rx="3"/>
  <rect x="380" y="720" width="40" height="100" fill="#795548" rx="3"/>
  <rect x="430" y="720" width="38" height="100" fill="#f44336" rx="3"/>
  <rect x="478" y="720" width="44" height="100" fill="#3f51b5" rx="3"/>
  <rect x="532" y="720" width="36" height="100" fill="#009688" rx="3"/>
  <rect x="578" y="720" width="42" height="100" fill="#cddc39" rx="3"/>
  <rect x="630" y="720" width="40" height="100" fill="#ffc107" rx="3"/>
  
  <!-- 右侧：写作者剪影 -->
  <rect x="800" y="250" width="400" height="600" fill="#f1f2f6" stroke="#ddd" stroke-width="2" rx="15"/>
  
  <!-- 写作者剪影 -->
  <circle cx="1000" cy="400" r="80" class="writer-bg"/>
  <rect x="950" y="480" width="100" height="150" class="writer-bg" rx="10"/>
  
  <!-- 桌子 -->
  <rect x="900" y="630" width="200" height="20" fill="#8d6e63" rx="5"/>
  
  <!-- 笔记本电脑 -->
  <rect x="950" y="600" width="100" height="60" fill="#333" rx="5"/>
  <rect x="960" y="610" width="80" height="40" fill="#f5f5f5" rx="3"/>
  
  <!-- 咖啡杯 -->
  <circle cx="920" cy="620" r="15" fill="#8d6e63"/>
  
  <!-- 书本 -->
  <rect x="1070" y="610" width="30" height="40" fill="#2196f3" rx="2"/>
  <rect x="1110" y="610" width="30" height="40" fill="#4caf50" rx="2"/>
  
  <!-- 核心问题区域 -->
  <rect x="1250" y="250" width="520" height="600" fill="#fff3cd" stroke="#ffc107" stroke-width="3" rx="15"/>
  <text x="1300" y="320" class="subtitle" fill="#f57f17">核心挑战：</text>
  
  <text x="1320" y="380" class="question-text">如何让内容...</text>
  
  <text x="1340" y="430" class="content-text" font-size="24px">• 不反感？</text>
  <text x="1360" y="460" class="content-text" font-size="20px">避免过度营销和骚扰</text>
  
  <text x="1340" y="510" class="content-text" font-size="24px">• 想互动？</text>
  <text x="1360" y="540" class="content-text" font-size="20px">引发用户参与和讨论</text>
  
  <text x="1340" y="590" class="content-text" font-size="24px">• 愿买单？</text>
  <text x="1360" y="620" class="content-text" font-size="20px">最终促成转化和成交</text>
  
  <!-- 内容策略预告 -->
  <rect x="1300" y="670" width="420" height="150" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="10"/>
  <text x="1320" y="710" class="content-text" font-weight="bold" fill="#2e7d32">内容策略核心：</text>
  <text x="1340" y="740" class="content-text" font-size="22px" fill="#2e7d32">价值优先，营销克制</text>
  <text x="1340" y="770" class="content-text" font-size="22px" fill="#2e7d32">建立信任，培养关系</text>
  <text x="1340" y="800" class="content-text" font-size="22px" fill="#2e7d32">持续输出，长期经营</text>
  
  <!-- 装饰性元素 -->
  <circle cx="50" cy="500" r="30" fill="#8d6e63" opacity="0.3"/>
  <text x="50" y="510" text-anchor="middle" class="content-text" fill="#5d4037" font-size="16px">内容</text>
  
  <circle cx="1870" cy="500" r="30" fill="#ffc107" opacity="0.3"/>
  <text x="1870" y="510" text-anchor="middle" class="content-text" fill="#f57f17" font-size="16px">为王</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 200 Q 200 180 300 200" stroke="#8d6e63" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1620 200 Q 1720 180 1820 200" stroke="#ffc107" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
