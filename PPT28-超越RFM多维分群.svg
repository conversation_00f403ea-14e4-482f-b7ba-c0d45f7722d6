<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 标题 -->
  <text x="960" y="100" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="#003d7a">超越RFM：让洞察更立体的"多维分群"</text>
  
  <!-- 三个层层递进的板块 -->
  
  <!-- 板块一：人口属性分群 -->
  <rect x="150" y="200" width="500" height="220" rx="20" fill="#ffe6e6" stroke="#cc3333" stroke-width="3"/>
  <text x="400" y="240" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#cc3333">人口属性分群</text>
  <text x="400" y="275" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#003d7a">(Demographic)</text>
  
  <text x="200" y="320" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ff6600">关键词：他是谁？</text>
  
  <text x="200" y="360" font-family="Microsoft YaHei" font-size="20" fill="#333">维度示例：</text>
  <text x="200" y="385" font-family="Microsoft YaHei" font-size="18" fill="#333">• 年龄段 (Z世代/青年/中年/银发)</text>
  <text x="200" y="410" font-family="Microsoft YaHei" font-size="18" fill="#333">• 职业 (学生/白领/蓝领/商务)</text>
  <text x="200" y="435" font-family="Microsoft YaHei" font-size="18" fill="#333">• 地理位置 (城市/乡镇/农村)</text>
  
  <!-- 板块二：行为属性分群 -->
  <rect x="710" y="200" width="500" height="220" rx="20" fill="#e6ffe6" stroke="#009900" stroke-width="3"/>
  <text x="960" y="240" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#009900">行为属性分群</text>
  <text x="960" y="275" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#003d7a">(Behavioral)</text>
  
  <text x="760" y="320" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ff6600">关键词：他做了什么？</text>
  
  <text x="760" y="360" font-family="Microsoft YaHei" font-size="20" fill="#333">维度示例：</text>
  <text x="760" y="385" font-family="Microsoft YaHei" font-size="18" fill="#333">• 终端类型 (5G手机/4G手机)</text>
  <text x="760" y="410" font-family="Microsoft YaHei" font-size="18" fill="#333">• 流量习惯 (月光族/保守派/夜猫子)</text>
  <text x="760" y="435" font-family="Microsoft YaHei" font-size="18" fill="#333">• APP使用行为 (高频/低频/从不使用)</text>
  
  <!-- 板块三：生命周期分群 -->
  <rect x="1270" y="200" width="500" height="220" rx="20" fill="#e6f3ff" stroke="#0066cc" stroke-width="3"/>
  <text x="1520" y="240" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#0066cc">生命周期分群</text>
  <text x="1520" y="275" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#003d7a">(Lifecycle)</text>
  
  <text x="1320" y="320" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ff6600">关键词：他处在哪个阶段？</text>
  
  <text x="1320" y="360" font-family="Microsoft YaHei" font-size="20" fill="#333">维度示例：</text>
  <text x="1320" y="385" font-family="Microsoft YaHei" font-size="18" fill="#333">• 导入期 (新用户)</text>
  <text x="1320" y="410" font-family="Microsoft YaHei" font-size="18" fill="#333">• 成长期、成熟期、衰退期</text>
  <text x="1320" y="435" font-family="Microsoft YaHei" font-size="18" fill="#333">• 流失期</text>
  
  <!-- 连接箭头 -->
  <polygon points="670,310 710,300 710,320" fill="#666"/>
  <polygon points="1230,310 1270,300 1270,320" fill="#666"/>
  
  <!-- 底部应用示例 -->
  <rect x="200" y="500" width="1520" height="400" rx="20" fill="#f8f8f8" stroke="#666" stroke-width="2"/>
  <text x="960" y="540" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#003d7a">应用示例：精准沟通策略</text>
  
  <!-- 示例对比 -->
  <rect x="250" y="580" width="650" height="280" rx="15" fill="#fff5f5" stroke="#cc3333" stroke-width="2"/>
  <text x="575" y="620" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#cc3333">西安大学生群体</text>
  <text x="575" y="660" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#003d7a">沟通重点</text>
  <text x="280" y="700" font-family="Microsoft YaHei" font-size="22" fill="#333">• 游戏加速</text>
  <text x="280" y="730" font-family="Microsoft YaHei" font-size="22" fill="#333">• 视频免流</text>
  <text x="280" y="760" font-family="Microsoft YaHei" font-size="22" fill="#333">• 校园社交</text>
  <text x="280" y="790" font-family="Microsoft YaHei" font-size="22" fill="#333">• 学生优惠</text>
  <text x="280" y="820" font-family="Microsoft YaHei" font-size="22" fill="#333">• 时尚潮流</text>
  
  <rect x="1020" y="580" width="650" height="280" rx="15" fill="#f5fff5" stroke="#009900" stroke-width="2"/>
  <text x="1345" y="620" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#009900">安康农村用户</text>
  <text x="1345" y="660" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#003d7a">沟通重点</text>
  <text x="1050" y="700" font-family="Microsoft YaHei" font-size="22" fill="#333">• 信号稳定</text>
  <text x="1050" y="730" font-family="Microsoft YaHei" font-size="22" fill="#333">• 资费实惠</text>
  <text x="1050" y="760" font-family="Microsoft YaHei" font-size="22" fill="#333">• 操作简单</text>
  <text x="1050" y="790" font-family="Microsoft YaHei" font-size="22" fill="#333">• 亲情服务</text>
  <text x="1050" y="820" font-family="Microsoft YaHei" font-size="22" fill="#333">• 本地化</text>
</svg>
