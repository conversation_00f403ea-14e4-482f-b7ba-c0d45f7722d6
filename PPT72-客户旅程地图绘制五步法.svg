<svg viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #2c3e50; }
      .step-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #34495e; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; line-height: 1.6; }
      .step-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 22px; fill: #2c3e50; line-height: 1.5; }
      .main-bg { fill: #f8f9fa; stroke: #e9ecef; stroke-width: 3; rx: 20; }
      .step-bg { fill: #e3f2fd; stroke: #2196f3; stroke-width: 2; rx: 15; }
      .number-circle { fill: #2196f3; stroke: #1976d2; stroke-width: 3; }
      .icon-bg { fill: #fff3e0; stroke: #ff9800; stroke-width: 2; rx: 8; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" text-anchor="middle" class="title">客户旅程地图绘制"五步法"</text>
  
  <!-- 主要内容区域 -->
  <rect x="100" y="150" width="1720" height="850" class="main-bg"/>
  
  <!-- 步骤1 -->
  <circle cx="200" cy="250" r="30" class="number-circle"/>
  <text x="200" y="260" text-anchor="middle" class="content-text" fill="white" font-weight="bold">1</text>
  
  <rect x="280" y="200" width="300" height="120" class="step-bg"/>
  <rect x="300" y="220" width="40" height="40" class="icon-bg"/>
  <text x="320" y="245" text-anchor="middle" class="content-text" fill="#e65100" font-size="16px">👤</text>
  
  <text x="360" y="235" class="step-title" fill="#1565c0">定主角、定剧本</text>
  <text x="360" y="260" class="step-text" fill="#1565c0">(Define)</text>
  <text x="300" y="290" class="step-text">选定一个Persona（画像）</text>
  <text x="300" y="310" class="step-text">和Scenario（场景）</text>
  
  <!-- 步骤2 -->
  <circle cx="200" cy="400" r="30" class="number-circle"/>
  <text x="200" y="410" text-anchor="middle" class="content-text" fill="white" font-weight="bold">2</text>
  
  <rect x="280" y="350" width="300" height="120" class="step-bg"/>
  <rect x="300" y="370" width="40" height="40" class="icon-bg"/>
  <text x="320" y="395" text-anchor="middle" class="content-text" fill="#e65100" font-size="16px">📋</text>
  
  <text x="360" y="385" class="step-title" fill="#1565c0">分阶段、分幕布</text>
  <text x="360" y="410" class="step-text" fill="#1565c0">(Structure)</text>
  <text x="300" y="440" class="step-text">将整个旅程划分为几个</text>
  <text x="300" y="460" class="step-text">大的、符合逻辑的阶段</text>
  
  <!-- 步骤3 -->
  <circle cx="200" cy="550" r="30" class="number-circle"/>
  <text x="200" y="560" text-anchor="middle" class="content-text" fill="white" font-weight="bold">3</text>
  
  <rect x="280" y="500" width="300" height="120" class="step-bg"/>
  <rect x="300" y="520" width="40" height="40" class="icon-bg"/>
  <text x="320" y="545" text-anchor="middle" class="content-text" fill="#e65100" font-size="16px">📝</text>
  
  <text x="360" y="535" class="step-title" fill="#1565c0">填剧情、填要素</text>
  <text x="360" y="560" class="step-text" fill="#1565c0">(Detail)</text>
  <text x="300" y="590" class="step-text">填充行为(Actions)、触点(Touchpoints)</text>
  <text x="300" y="610" class="step-text">想法(Thoughts)、情绪(Feelings)</text>
  
  <!-- 步骤4 -->
  <circle cx="200" cy="700" r="30" class="number-circle"/>
  <text x="200" y="710" text-anchor="middle" class="content-text" fill="white" font-weight="bold">4</text>
  
  <rect x="280" y="650" width="300" height="120" class="step-bg"/>
  <rect x="300" y="670" width="40" height="40" class="icon-bg"/>
  <text x="320" y="695" text-anchor="middle" class="content-text" fill="#e65100" font-size="16px">📈</text>
  
  <text x="360" y="685" class="step-title" fill="#1565c0">画曲线、找波谷</text>
  <text x="360" y="710" class="step-text" fill="#1565c0">(Visualize)</text>
  <text x="300" y="740" class="step-text">绘制情绪曲线，识别出</text>
  <text x="300" y="760" class="step-text">情绪最低点的"痛点"</text>
  
  <!-- 步骤5 -->
  <circle cx="200" cy="850" r="30" class="number-circle"/>
  <text x="200" y="860" text-anchor="middle" class="content-text" fill="white" font-weight="bold">5</text>
  
  <rect x="280" y="800" width="300" height="120" class="step-bg"/>
  <rect x="300" y="820" width="40" height="40" class="icon-bg"/>
  <text x="320" y="845" text-anchor="middle" class="content-text" fill="#e65100" font-size="16px">💡</text>
  
  <text x="360" y="835" class="step-title" fill="#1565c0">寻机会、脑风暴</text>
  <text x="360" y="860" class="step-text" fill="#1565c0">(Ideate)</text>
  <text x="300" y="890" class="step-text">针对每一个"痛点"，</text>
  <text x="300" y="910" class="step-text">寻找"机会点"</text>
  
  <!-- 右侧流程图 -->
  <rect x="650" y="200" width="1120" height="700" fill="#f1f2f6" stroke="#ddd" stroke-width="2" rx="15"/>
  <text x="1210" y="240" text-anchor="middle" class="step-title">五步法流程图</text>
  
  <!-- 流程箭头 -->
  <rect x="700" y="280" width="200" height="60" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="10"/>
  <text x="800" y="315" text-anchor="middle" class="step-text" fill="#2e7d32">1. 定主角定剧本</text>
  
  <polygon points="920,310 970,295 970,305 1000,305 1000,325 970,325 970,335" fill="#7f8c8d"/>
  
  <rect x="1020" y="280" width="200" height="60" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="10"/>
  <text x="1120" y="315" text-anchor="middle" class="step-text" fill="#2e7d32">2. 分阶段分幕布</text>
  
  <polygon points="1120,360 1135,410 1105,410" fill="#7f8c8d"/>
  
  <rect x="1020" y="430" width="200" height="60" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="10"/>
  <text x="1120" y="465" text-anchor="middle" class="step-text" fill="#2e7d32">3. 填剧情填要素</text>
  
  <polygon points="1000,460 950,445 950,455 920,455 920,475 950,475 950,485" fill="#7f8c8d"/>
  
  <rect x="700" y="430" width="200" height="60" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="10"/>
  <text x="800" y="465" text-anchor="middle" class="step-text" fill="#2e7d32">4. 画曲线找波谷</text>
  
  <polygon points="800,510 815,560 785,560" fill="#7f8c8d"/>
  
  <rect x="700" y="580" width="200" height="60" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="10"/>
  <text x="800" y="615" text-anchor="middle" class="step-text" fill="#2e7d32">5. 寻机会脑风暴</text>
  
  <!-- 装饰性元素 -->
  <circle cx="50" cy="500" r="30" fill="#2196f3" opacity="0.3"/>
  <text x="50" y="510" text-anchor="middle" class="content-text" fill="#1565c0" font-size="16px">五步</text>
  
  <circle cx="1870" cy="500" r="30" fill="#4caf50" opacity="0.3"/>
  <text x="1870" y="510" text-anchor="middle" class="content-text" fill="#2e7d32" font-size="16px">绘制</text>
  
  <!-- 装饰性弧线 -->
  <path d="M 100 150 Q 200 130 300 150" stroke="#2196f3" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M 1620 150 Q 1720 130 1820 150" stroke="#4caf50" stroke-width="3" fill="none" opacity="0.6"/>
</svg>
